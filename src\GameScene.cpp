#include "../include/GameScene.hpp"
#include <SFML/Graphics.hpp>
#include "../include/GameConfig.hpp"
#include "../include/Racket.hpp"
#include <fstream>
#include <iostream>

GameScene::GameScene(int width, int height, const char *title)
{
    windowWidth = width;
    windowHeight = height;

    // 初始化游戏配置
    GameConfig::initialize(width, height);

    window = new sf::RenderWindow(sf::VideoMode(width, height), title);
    window->setFramerateLimit(60);

    // 创建物理世界
    physicsWorld = new PhysicsWorld(windowWidth, windowHeight);

    // 初始化玩家
    player1 = new Player(1, sf::Color::Green, physicsWorld->getWorldId());
    player1->setPosition(GameConfig::getPlayer1StartPos().x, GameConfig::getPlayer1StartPos().y);
    player2 = new Player(2, sf::Color::Blue, physicsWorld->getWorldId());
    player2->setPosition(GameConfig::getPlayer2StartPos().x, GameConfig::getPlayer2StartPos().y);

    // 初始化羽毛球 - 在网的上方一点点
    shuttlecock = new Shuttlecock(physicsWorld->getWorldId(), sf::Vector2f(GameConfig::getNetX(), GameConfig::getNetY() - GameConfig::getNetHeight() / 2.0f - 50.0f));

    clock.restart();
    // 进入游戏循环
    while (window->isOpen())
    {

        sf::Event event;
        if (window->pollEvent(event))
        {
            if (event.type == sf::Event::Closed)
            {
                window->close();
            }
            else if (event.type == sf::Event::KeyPressed || event.type == sf::Event::KeyReleased)
            {
                // 处理玩家输入
                processGamingInput(event);
            }
        }
        controlPlayer();
        renderGame();
    }
}

void GameScene::processGamingInput(sf::Event &event)
{
    if (event.type == sf::Event::KeyPressed)
    {
        pressedKeys.insert(event.key.code);
    }
    if (event.type == sf::Event::KeyReleased)
    {
        pressedKeys.erase(event.key.code);
    }
}

void GameScene::controlPlayer()
{

    // 玩家1控制 (A/D移动, W/S球拍, Q跳跃)
    if (pressedKeys.count(sf::Keyboard::A))
    {
        Direction leftDir(sf::Vector2f(-1.0f, 0.0f));
        player1->move(leftDir);
    }
    if (pressedKeys.count(sf::Keyboard::D))
    {
        Direction rightDir(sf::Vector2f(1.0f, 0.0f));
        player1->move(rightDir);
    }
    // 玩家1挥拍控制
    bool player1Swinging = false;
    if (pressedKeys.count(sf::Keyboard::W))
    {
        Direction upDir(sf::Vector2f(0.0f, -1.0f));
        player1->swing(upDir);
        player1Swinging = true;
    }
    if (pressedKeys.count(sf::Keyboard::S))
    {
        Direction downDir(sf::Vector2f(0.0f, 1.0f));
        player1->swing(downDir);
        player1Swinging = true;
    }
    // 如果没有挥拍按键，重置挥拍状态
    if (!player1Swinging && player1->getRacket())
    {
        player1->getRacket()->setSwinging(0);
    }
    if (pressedKeys.count(sf::Keyboard::Q))
    {
        Direction upDir(sf::Vector2f(0.0f, 1.0f));
        player1->move(upDir);
    }

    // 玩家2控制 (方向键移动, Up/Down球拍, RShift跳跃)
    if (pressedKeys.count(sf::Keyboard::Left))
    {
        Direction leftDir(sf::Vector2f(-1.0f, 0.0f));
        player2->move(leftDir);
    }
    if (pressedKeys.count(sf::Keyboard::Right))
    {
        Direction rightDir(sf::Vector2f(1.0f, 0.0f));
        player2->move(rightDir);
    }
    // 玩家2挥拍控制
    bool player2Swinging = false;
    if (pressedKeys.count(sf::Keyboard::Up))
    {
        Direction upDir(sf::Vector2f(0.0f, -1.0f));
        player2->swing(upDir);
        player2Swinging = true;
    }
    if (pressedKeys.count(sf::Keyboard::Down))
    {
        Direction downDir(sf::Vector2f(0.0f, 1.0f));
        player2->swing(downDir);
        player2Swinging = true;
    }
    // 如果没有挥拍按键，重置挥拍状态
    if (!player2Swinging && player2->getRacket())
    {
        player2->getRacket()->setSwinging(0);
    }
    if (pressedKeys.count(sf::Keyboard::RShift))
    {
        Direction upDir(sf::Vector2f(0.0f, 1.0f));
        player2->move(upDir);
    }
}

void GameScene::renderGame()
{
    float deltaTime = clock.restart().asSeconds();

    // 背景设置成白色
    window->clear(sf::Color::White);

    // 更新物理世界
    physicsWorld->step(deltaTime);

    // 渲染场地
    physicsWorld->render(*window);

    // 渲染玩家和球拍
    player1->draw(*window);
    player2->draw(*window);

    // 渲染羽毛球
    shuttlecock->render(*window);

    window->display();
}

GameScene::~GameScene()
{
    delete player1;
    delete player2;
    delete shuttlecock;
    delete physicsWorld;
    delete window;
}

void GameScene::Game()
{
    // TODO
}