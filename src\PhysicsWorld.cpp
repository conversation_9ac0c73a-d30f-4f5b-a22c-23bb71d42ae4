#include "../include/PhysicsWorld.hpp"
#include <fstream>

// 定义转换比例：使用GameConfig中的值
const float PhysicsWorld::PIXELS_PER_METER = GameConfig::PIXELS_PER_METER;

PhysicsWorld::PhysicsWorld(int windowWidth, int windowHeight)
    : windowWidth(windowWidth), windowHeight(windowHeight)
{
    // 创建物理世界，设置重力
    b2WorldDef worldDef = b2DefaultWorldDef();
    worldDef.gravity = {0.0f, GameConfig::GRAVITY}; // 向下的重力
    worldId = b2CreateWorld(&worldDef);

    // 创建场地元素
    createGround();
    createWalls();
    createNet();
    createCeiling();
    setupRenderShapes();
}

PhysicsWorld::~PhysicsWorld()
{
    b2DestroyWorld(worldId);
}

void PhysicsWorld::step(float deltaTime)
{
    // Box2D 3.0 API
    b2World_Step(worldId, deltaTime, 4);

    // 处理碰撞
    handleCollisions();
}

void PhysicsWorld::handleCollisions()
{
    // Box2D 3.0的碰撞检测会自动处理
    // 物理引擎会自动处理球拍与地面、网、球的碰撞
    // 这里可以添加特殊的碰撞响应逻辑
}

void PhysicsWorld::render(sf::RenderWindow &window)
{
    window.draw(groundShape);
    window.draw(leftWallShape);
    window.draw(rightWallShape);
    window.draw(netShape);
    window.draw(ceilingShape); // 渲染天花板
}

sf::Vector2f PhysicsWorld::b2ToSf(const b2Vec2 &vec)
{
    return sf::Vector2f(vec.x * PIXELS_PER_METER, vec.y * PIXELS_PER_METER);
}

b2Vec2 PhysicsWorld::sfToB2(const sf::Vector2f &vec)
{
    return {vec.x / PIXELS_PER_METER, vec.y / PIXELS_PER_METER};
}

void PhysicsWorld::createGround()
{
    // 地面位置在窗口底部
    float groundY = GameConfig::getGroundY() + GameConfig::getGroundThickness() / 2.0f;

    b2BodyDef groundBodyDef = b2DefaultBodyDef();
    groundBodyDef.position = {windowWidth / 2.0f / PIXELS_PER_METER, groundY / PIXELS_PER_METER};
    ground = b2CreateBody(worldId, &groundBodyDef);

    b2Polygon groundBox = b2MakeBox((windowWidth / 2.0f) / PIXELS_PER_METER,
                                    (GameConfig::getGroundThickness() / 2.0f) / PIXELS_PER_METER);

    b2ShapeDef shapeDef = b2DefaultShapeDef();
    b2ShapeId groundShapeId = b2CreatePolygonShape(ground, &shapeDef, &groundBox);

    // 设置完全非弹性碰撞
    b2Shape_SetRestitution(groundShapeId, 0.0f);
}

void PhysicsWorld::createWalls()
{
    // 左墙
    b2BodyDef leftWallBodyDef = b2DefaultBodyDef();
    leftWallBodyDef.position = {GameConfig::getLeftWallX() / PIXELS_PER_METER, windowHeight / 2.0f / PIXELS_PER_METER};
    leftWall = b2CreateBody(worldId, &leftWallBodyDef);

    b2Polygon leftWallBox = b2MakeBox((GameConfig::getWallThickness() / 2.0f) / PIXELS_PER_METER,
                                      (windowHeight / 2.0f) / PIXELS_PER_METER);
    b2ShapeDef leftShapeDef = b2DefaultShapeDef();
    b2ShapeId leftWallShapeId = b2CreatePolygonShape(leftWall, &leftShapeDef, &leftWallBox);

    // 设置完全非弹性碰撞，不反弹
    b2Shape_SetRestitution(leftWallShapeId, 0.0f);

    // 右墙
    b2BodyDef rightWallBodyDef = b2DefaultBodyDef();
    rightWallBodyDef.position = {GameConfig::getRightWallX() / PIXELS_PER_METER, windowHeight / 2.0f / PIXELS_PER_METER};
    rightWall = b2CreateBody(worldId, &rightWallBodyDef);

    b2Polygon rightWallBox = b2MakeBox((GameConfig::getWallThickness() / 2.0f) / PIXELS_PER_METER,
                                       (windowHeight / 2.0f) / PIXELS_PER_METER);
    b2ShapeDef rightShapeDef = b2DefaultShapeDef();
    b2ShapeId rightWallShapeId = b2CreatePolygonShape(rightWall, &rightShapeDef, &rightWallBox);

    // 设置完全非弹性碰撞，不反弹
    b2Shape_SetRestitution(rightWallShapeId, 0.0f);
}

void PhysicsWorld::createNet()
{
    // 羽毛球网在场地中央
    float netX = GameConfig::getNetX();
    float netY = GameConfig::getNetY();

    b2BodyDef netBodyDef = b2DefaultBodyDef();
    netBodyDef.position = {netX / PIXELS_PER_METER, netY / PIXELS_PER_METER};
    net = b2CreateBody(worldId, &netBodyDef);

    b2Polygon netBox = b2MakeBox((GameConfig::getNetWidth() / 2.0f) / PIXELS_PER_METER,
                                 (GameConfig::getNetHeight() / 2.0f) / PIXELS_PER_METER);
    b2ShapeDef netShapeDef = b2DefaultShapeDef();
    b2ShapeId netShapeId = b2CreatePolygonShape(net, &netShapeDef, &netBox);

    // 设置完全非弹性碰撞，不反弹
    b2Shape_SetRestitution(netShapeId, 0.0f);
}

void PhysicsWorld::createCeiling()
{
    // 天花板在屏幕顶部
    float ceilingThickness = 20.0f;            // 天花板厚度
    float ceilingY = -ceilingThickness / 2.0f; // 在屏幕顶部上方

    b2BodyDef ceilingBodyDef = b2DefaultBodyDef();
    ceilingBodyDef.position = {windowWidth / 2.0f / PIXELS_PER_METER, ceilingY / PIXELS_PER_METER};
    ceiling = b2CreateBody(worldId, &ceilingBodyDef);

    b2Polygon ceilingBox = b2MakeBox((windowWidth / 2.0f) / PIXELS_PER_METER,
                                     (ceilingThickness / 2.0f) / PIXELS_PER_METER);
    b2ShapeDef ceilingShapeDef = b2DefaultShapeDef();
    b2ShapeId ceilingShapeId = b2CreatePolygonShape(ceiling, &ceilingShapeDef, &ceilingBox);

    // 设置完全非弹性碰撞，不反弹
    b2Shape_SetRestitution(ceilingShapeId, 0.0f);

    //***********
    std::ofstream debugLog("physics_debug.txt", std::ios::app);
    debugLog << "Ceiling created at Y: " << ceilingY << std::endl;
    debugLog.close();
    //***********
}

void PhysicsWorld::setupRenderShapes()
{
    // 地面渲染形状
    groundShape.setSize(sf::Vector2f(windowWidth, GameConfig::getGroundThickness()));
    groundShape.setPosition(0, GameConfig::getGroundY());
    groundShape.setFillColor(sf::Color(139, 69, 19)); // 棕色地面

    // 左墙渲染形状
    leftWallShape.setSize(sf::Vector2f(GameConfig::getWallThickness(), windowHeight));
    leftWallShape.setPosition(0, 0);
    leftWallShape.setFillColor(sf::Color(128, 128, 128)); // 灰色墙壁

    // 右墙渲染形状
    rightWallShape.setSize(sf::Vector2f(GameConfig::getWallThickness(), windowHeight));
    rightWallShape.setPosition(windowWidth - GameConfig::getWallThickness(), 0);
    rightWallShape.setFillColor(sf::Color(128, 128, 128)); // 灰色墙壁

    // 网渲染形状
    netShape.setSize(sf::Vector2f(GameConfig::getNetWidth(), GameConfig::getNetHeight()));
    netShape.setPosition(GameConfig::getNetX() - GameConfig::getNetWidth() / 2.0f,
                         GameConfig::getNetY() - GameConfig::getNetHeight() / 2.0f);
    netShape.setFillColor(sf::Color::Black); // 黑色网

    // 天花板渲染形状
    ceilingShape.setSize(sf::Vector2f(windowWidth, 20.0f));
    ceilingShape.setPosition(0, -20.0f);        // 在屏幕顶部上方
    ceilingShape.setFillColor(sf::Color::Blue); // 蓝色天花板
}
