# Box2D 3.0 完整使用指南

## 详细目录

### 1. [基础概念](#1-基础概念)
   - 1.1 [Box2D简介](#11-box2d简介)
   - 1.2 [坐标系统](#12-坐标系统)
   - 1.3 [单位系统](#13-单位系统)
   - 1.4 [时间步长](#14-时间步长)

### 2. [World (世界)](#2-world-世界)
   - 2.1 [世界概述](#21-世界概述)
   - 2.2 [世界创建与配置](#22-世界创建与配置)
   - 2.3 [物理模拟](#23-物理模拟)
   - 2.4 [世界查询](#24-世界查询)
   - 2.5 [世界事件](#25-世界事件)

### 3. [Body (物体)](#3-body-物体)
   - 3.1 [物体概述](#31-物体概述)
   - 3.2 [物体类型](#32-物体类型)
   - 3.3 [物体创建与销毁](#33-物体创建与销毁)
   - 3.4 [位置与变换](#34-位置与变换)
   - 3.5 [速度控制](#35-速度控制)
   - 3.6 [力与冲量](#36-力与冲量)
   - 3.7 [质量属性](#37-质量属性)
   - 3.8 [物体状态](#38-物体状态)
   - 3.9 [用户数据](#39-用户数据)

### 4. [Shape (形状)](#4-shape-形状)
   - 4.1 [形状概述](#41-形状概述)
   - 4.2 [形状类型](#42-形状类型)
   - 4.3 [圆形 (Circle)](#43-圆形-circle)
   - 4.4 [多边形 (Polygon)](#44-多边形-polygon)
   - 4.5 [胶囊 (Capsule)](#45-胶囊-capsule)
   - 4.6 [线段 (Segment)](#46-线段-segment)
   - 4.7 [形状属性](#47-形状属性)
   - 4.8 [碰撞过滤](#48-碰撞过滤)
   - 4.9 [传感器](#49-传感器)

### 5. [Joint (关节)](#5-joint-关节)
   - 5.1 [关节概述](#51-关节概述)
   - 5.2 [旋转关节 (Revolute)](#52-旋转关节-revolute)
   - 5.3 [平移关节 (Prismatic)](#53-平移关节-prismatic)
   - 5.4 [距离关节 (Distance)](#54-距离关节-distance)
   - 5.5 [焊接关节 (Weld)](#55-焊接关节-weld)
   - 5.6 [轮子关节 (Wheel)](#56-轮子关节-wheel)
   - 5.7 [滑轮关节 (Pulley)](#57-滑轮关节-pulley)
   - 5.8 [齿轮关节 (Gear)](#58-齿轮关节-gear)
   - 5.9 [马达关节 (Motor)](#59-马达关节-motor)

### 6. [碰撞检测](#6-碰撞检测)
   - 6.1 [碰撞概述](#61-碰撞概述)
   - 6.2 [碰撞回调](#62-碰撞回调)
   - 6.3 [射线投射](#63-射线投射)
   - 6.4 [形状查询](#64-形状查询)
   - 6.5 [AABB查询](#65-aabb查询)

### 7. [ID系统](#7-id系统)
   - 7.1 [ID概述](#71-id概述)
   - 7.2 [ID类型](#72-id类型)
   - 7.3 [ID验证](#73-id验证)
   - 7.4 [ID生命周期](#74-id生命周期)

### 8. [数学工具](#8-数学工具)
   - 8.1 [向量操作](#81-向量操作)
   - 8.2 [旋转操作](#82-旋转操作)
   - 8.3 [变换操作](#83-变换操作)
   - 8.4 [几何计算](#84-几何计算)

### 9. [实际应用示例](#9-实际应用示例)
   - 9.1 [基础物理世界](#91-基础物理世界)
   - 9.2 [平台游戏](#92-平台游戏)
   - 9.3 [车辆模拟](#93-车辆模拟)
   - 9.4 [机械装置](#94-机械装置)
   - 9.5 [粒子系统](#95-粒子系统)

### 10. [性能优化](#10-性能优化)
   - 10.1 [性能概述](#101-性能概述)
   - 10.2 [内存管理](#102-内存管理)
   - 10.3 [计算优化](#103-计算优化)
   - 10.4 [调试工具](#104-调试工具)

### 11. [常见问题与解决方案](#11-常见问题与解决方案)
   - 11.1 [物体穿透](#111-物体穿透)
   - 11.2 [不稳定的关节](#112-不稳定的关节)
   - 11.3 [性能问题](#113-性能问题)
   - 11.4 [精度问题](#114-精度问题)

---

## 1. 基础概念

### 1.1 Box2D简介

Box2D是一个开源的2D物理引擎，专为游戏开发设计。它提供了：
- **刚体动力学**: 模拟物体的运动和碰撞
- **关节系统**: 连接物体创建复杂机械结构
- **碰撞检测**: 高效的碰撞检测和响应
- **约束求解**: 处理物理约束和限制

#### 主要特性
- 连续碰撞检测 (CCD)
- 稳定的堆叠
- 接触回调
- 可调节的时间步长
- 调试渲染

### 1.2 坐标系统

Box2D使用右手坐标系：
- **X轴**: 向右为正
- **Y轴**: 向上为正
- **角度**: 逆时针为正，单位为弧度
- **原点**: 通常在屏幕左下角

#### 坐标转换
```cpp
// 屏幕坐标转物理坐标 (假设屏幕高度为screenHeight)
float physicsY = screenHeight - screenY;

// 角度转换
float radians = degrees * (M_PI / 180.0f);
float degrees = radians * (180.0f / M_PI);
```

### 1.3 单位系统

Box2D使用MKS (米-千克-秒) 单位系统：
- **长度**: 米 (m)
- **质量**: 千克 (kg)
- **时间**: 秒 (s)
- **力**: 牛顿 (N = kg⋅m/s²)
- **扭矩**: 牛顿⋅米 (N⋅m)

#### 推荐的物理尺度
- **小物体**: 0.1 - 1 米
- **大物体**: 1 - 10 米
- **速度**: < 50 m/s
- **重力**: 9.8 m/s² (地球重力)

#### 单位转换示例
```cpp
// 像素到米的转换
const float PIXELS_PER_METER = 100.0f;
float metersToPixels(float meters) { return meters * PIXELS_PER_METER; }
float pixelsToMeters(float pixels) { return pixels / PIXELS_PER_METER; }
```

### 1.4 时间步长

时间步长决定了物理模拟的精度和性能：

#### 固定时间步长 (推荐)
```cpp
const float timeStep = 1.0f / 60.0f;  // 60 FPS
const int velocityIterations = 8;
const int positionIterations = 3;

// 每帧调用
b2World_Step(worldId, timeStep, velocityIterations);
```

#### 可变时间步长
```cpp
float deltaTime = GetFrameTime();  // 获取帧时间
deltaTime = std::min(deltaTime, 1.0f/30.0f);  // 限制最大时间步
b2World_Step(worldId, deltaTime, 8);
```

#### 时间步长选择指南
- **60 FPS (1/60s)**: 标准选择，平衡性能和精度
- **30 FPS (1/30s)**: 性能优先，精度稍低
- **120 FPS (1/120s)**: 高精度，性能要求高

---

## 2. World (世界)

### 2.1 世界概述

World是Box2D物理模拟的核心容器，负责：
- 管理所有物体 (Bodies)
- 管理所有关节 (Joints)
- 执行碰撞检测
- 求解物理约束
- 处理连续碰撞检测 (CCD)

### 2.2 世界创建与配置

#### 世界定义结构
```cpp
typedef struct b2WorldDef {
    b2Vec2 gravity;              // 重力向量
    float restitutionThreshold;  // 恢复阈值
    float contactPushoutVelocity; // 接触推出速度
    float contactHertz;          // 接触频率
    float contactDampingRatio;   // 接触阻尼比
    float jointHertz;            // 关节频率
    float jointDampingRatio;     // 关节阻尼比
    bool enableSleep;            // 启用睡眠
    bool enableContinuous;       // 启用连续碰撞检测
    int workerCount;             // 工作线程数
} b2WorldDef;
```

#### 创建世界
```cpp
// 基础创建
b2WorldDef worldDef = b2DefaultWorldDef();
worldDef.gravity = {0.0f, -9.8f};  // 向下的重力
b2WorldId worldId = b2CreateWorld(&worldDef);

// 高级配置
b2WorldDef advancedDef = b2DefaultWorldDef();
advancedDef.gravity = {0.0f, -9.8f};
advancedDef.enableSleep = true;           // 启用睡眠优化
advancedDef.enableContinuous = true;      // 启用CCD
advancedDef.contactHertz = 30.0f;         // 接触刚度
advancedDef.contactDampingRatio = 1.0f;   // 接触阻尼
advancedDef.workerCount = 4;              // 多线程
b2WorldId worldId = b2CreateWorld(&advancedDef);
```

#### 销毁世界
```cpp
// 销毁世界 (自动销毁所有物体和关节)
b2DestroyWorld(worldId);
```

### 2.3 物理模拟

#### 基础模拟步进
```cpp
// 标准模拟循环
float timeStep = 1.0f / 60.0f;
int subStepCount = 4;

b2World_Step(worldId, timeStep, subStepCount);
// 参数说明:
// - worldId: 世界ID
// - timeStep: 时间步长 (秒)
// - subStepCount: 子步数 (提高精度)
```

#### 高级模拟控制
```cpp
// 可变时间步长
float deltaTime = GetFrameTime();
deltaTime = b2ClampFloat(deltaTime, 1.0f/120.0f, 1.0f/30.0f);
b2World_Step(worldId, deltaTime, 4);

// 暂停/恢复模拟
bool isPaused = false;
if (!isPaused) {
    b2World_Step(worldId, timeStep, 4);
}
```

#### 模拟参数调优
```cpp
// 获取模拟统计信息
b2Counters counters = b2World_GetCounters(worldId);
printf("Bodies: %d, Contacts: %d, Joints: %d\n",
       counters.bodyCount, counters.contactCount, counters.jointCount);

// 获取性能分析
b2Profile profile = b2World_GetProfile(worldId);
printf("Step time: %.2f ms\n", profile.step * 1000.0f);
```

### 2.4 世界查询

#### 基础查询
```cpp
// 获取对象数量
int bodyCount = b2World_GetBodyCount(worldId);
int jointCount = b2World_GetJointCount(worldId);
int contactCount = b2World_GetContactCount(worldId);

// 检查世界是否有效
bool isValid = B2_IS_NON_NULL(worldId);
```

#### 遍历对象
```cpp
// 遍历所有物体
b2BodyId bodyId = b2World_GetBodyList(worldId);
while (B2_IS_NON_NULL(bodyId)) {
    // 处理物体
    ProcessBody(bodyId);
    bodyId = b2Body_GetNext(bodyId);
}

// 遍历所有关节
b2JointId jointId = b2World_GetJointList(worldId);
while (B2_IS_NON_NULL(jointId)) {
    // 处理关节
    ProcessJoint(jointId);
    jointId = b2Joint_GetNext(jointId);
}
```

### 2.5 世界事件

#### 碰撞事件
```cpp
// 设置碰撞事件监听器
b2WorldEvents events = b2World_GetEvents(worldId);

// 处理开始接触事件
for (int i = 0; i < events.beginContactCount; ++i) {
    b2ContactBeginTouchEvent* event = events.beginContactEvents + i;
    OnContactBegin(event->shapeIdA, event->shapeIdB);
}

// 处理结束接触事件
for (int i = 0; i < events.endContactCount; ++i) {
    b2ContactEndTouchEvent* event = events.endContactEvents + i;
    OnContactEnd(event->shapeIdA, event->shapeIdB);
}
```

#### 传感器事件
```cpp
// 处理传感器事件
for (int i = 0; i < events.sensorBeginCount; ++i) {
    b2SensorBeginTouchEvent* event = events.sensorBeginEvents + i;
    OnSensorEnter(event->sensorShapeId, event->visitorShapeId);
}

for (int i = 0; i < events.sensorEndCount; ++i) {
    b2SensorEndTouchEvent* event = events.sensorEndEvents + i;
    OnSensorExit(event->sensorShapeId, event->visitorShapeId);
}
```

---

## 3. Body (物体)

### 3.1 物体概述

Body (物体) 是Box2D中的刚体，代表物理世界中的一个对象。每个物体：
- 有位置和旋转
- 可以附加多个形状 (Shape)
- 有质量属性
- 可以通过关节连接到其他物体

### 3.2 物体类型

#### b2_staticBody (静态物体)
- **特性**: 不移动，无限质量，零速度
- **用途**: 地面、墙壁、平台
- **性能**: 最高效，不参与动力学计算
```cpp
bodyDef.type = b2_staticBody;
```

#### b2_dynamicBody (动态物体)
- **特性**: 受力影响，有质量，可以移动和旋转
- **用途**: 玩家、敌人、可移动物体
- **性能**: 需要完整的动力学计算
```cpp
bodyDef.type = b2_dynamicBody;
```

#### b2_kinematicBody (运动学物体)
- **特性**: 由代码控制移动，无限质量，不受力影响
- **用途**: 移动平台、电梯、门
- **性能**: 中等，只计算运动学
```cpp
bodyDef.type = b2_kinematicBody;
```

### 3.3 物体创建与销毁

#### 物体定义结构
```cpp
typedef struct b2BodyDef {
    b2BodyType type;              // 物体类型
    b2Vec2 position;              // 初始位置
    b2Rot rotation;               // 初始旋转
    b2Vec2 linearVelocity;        // 初始线性速度
    float angularVelocity;        // 初始角速度
    float linearDamping;          // 线性阻尼
    float angularDamping;         // 角阻尼
    float gravityScale;           // 重力缩放
    bool allowSleep;              // 允许睡眠
    bool isAwake;                 // 初始唤醒状态
    bool fixedRotation;           // 固定旋转
    bool isBullet;                // 子弹模式 (CCD)
    bool isEnabled;               // 启用状态
    void* userData;               // 用户数据
} b2BodyDef;
```

#### 创建物体
```cpp
// 基础创建
b2BodyDef bodyDef = b2DefaultBodyDef();
bodyDef.type = b2_dynamicBody;
bodyDef.position = {0.0f, 4.0f};
b2BodyId bodyId = b2CreateBody(worldId, &bodyDef);

// 高级配置
b2BodyDef advancedDef = b2DefaultBodyDef();
advancedDef.type = b2_dynamicBody;
advancedDef.position = {0.0f, 4.0f};
advancedDef.rotation = b2MakeRot(0.25f * M_PI);  // 45度
advancedDef.linearVelocity = {2.0f, 0.0f};       // 初始速度
advancedDef.angularVelocity = 1.0f;              // 初始角速度
advancedDef.linearDamping = 0.1f;                // 线性阻尼
advancedDef.angularDamping = 0.1f;               // 角阻尼
advancedDef.gravityScale = 1.0f;                 // 正常重力
advancedDef.allowSleep = true;                   // 允许睡眠
advancedDef.isBullet = false;                    // 非子弹模式
advancedDef.fixedRotation = false;               // 允许旋转
b2BodyId bodyId = b2CreateBody(worldId, &advancedDef);
```

#### 销毁物体
```cpp
// 销毁物体 (自动销毁所有附加的形状和关节)
b2DestroyBody(bodyId);

// 检查物体是否有效
if (B2_IS_NON_NULL(bodyId)) {
    b2DestroyBody(bodyId);
}
```

### 3.4 位置与变换

#### 获取位置和旋转
```cpp
// 获取物体位置
b2Vec2 position = b2Body_GetPosition(bodyId);
// 返回值: 物体的世界坐标位置

// 获取物体旋转
b2Rot rotation = b2Body_GetRotation(bodyId);
float angle = b2Rot_GetAngle(rotation);  // 转换为角度
// 返回值: 物体的旋转信息

// 获取物体变换
b2Transform transform = b2Body_GetTransform(bodyId);
// 返回值: 包含位置和旋转的变换矩阵
```

#### 设置位置和旋转
```cpp
// 设置物体变换
b2Vec2 newPosition = {5.0f, 3.0f};
b2Rot newRotation = b2MakeRot(0.5f * M_PI);  // 90度
b2Body_SetTransform(bodyId, newPosition, newRotation);
// 参数: bodyId - 物体ID, position - 新位置, rotation - 新旋转
// 作用: 直接设置物体的位置和旋转 (瞬移)

// 注意: 直接设置变换可能导致物体穿透，谨慎使用
```

#### 坐标转换
```cpp
// 世界坐标转本地坐标
b2Vec2 worldPoint = {10.0f, 5.0f};
b2Vec2 localPoint = b2Body_GetLocalPoint(bodyId, worldPoint);

// 本地坐标转世界坐标
b2Vec2 localPoint = {1.0f, 0.0f};
b2Vec2 worldPoint = b2Body_GetWorldPoint(bodyId, localPoint);

// 世界向量转本地向量
b2Vec2 worldVector = {1.0f, 1.0f};
b2Vec2 localVector = b2Body_GetLocalVector(bodyId, worldVector);

// 本地向量转世界向量
b2Vec2 localVector = {0.0f, 1.0f};
b2Vec2 worldVector = b2Body_GetWorldVector(bodyId, localVector);
```

### 3.5 速度控制

#### 获取速度
```cpp
// 获取线性速度
b2Vec2 velocity = b2Body_GetLinearVelocity(bodyId);
// 返回值: 物体的线性速度向量 (m/s)

// 获取角速度
float angularVelocity = b2Body_GetAngularVelocity(bodyId);
// 返回值: 物体的角速度 (弧度/秒)

// 获取某点的速度
b2Vec2 worldPoint = {5.0f, 3.0f};
b2Vec2 pointVelocity = b2Body_GetLinearVelocityFromWorldPoint(bodyId, worldPoint);
// 返回值: 指定世界坐标点的速度
```

#### 设置速度
```cpp
// 设置线性速度
b2Vec2 velocity = {5.0f, 0.0f};  // 5 m/s 向右
b2Body_SetLinearVelocity(bodyId, velocity);
// 参数: bodyId - 物体ID, velocity - 速度向量
// 作用: 直接设置物体的移动速度

// 设置角速度
float omega = 2.0f;  // 2 弧度/秒
b2Body_SetAngularVelocity(bodyId, omega);
// 参数: bodyId - 物体ID, omega - 角速度
// 作用: 直接设置物体的旋转速度

// 限制速度
b2Vec2 currentVel = b2Body_GetLinearVelocity(bodyId);
float maxSpeed = 10.0f;
if (b2Length(currentVel) > maxSpeed) {
    b2Vec2 limitedVel = b2Normalize(currentVel);
    limitedVel = b2MulSV(maxSpeed, limitedVel);
    b2Body_SetLinearVelocity(bodyId, limitedVel);
}
```

#### 位置和变换
```cpp
// 获取物体位置
b2Vec2 b2Body_GetPosition(b2BodyId bodyId);
// 返回值: 物体的世界坐标位置

// 获取物体旋转
b2Rot b2Body_GetRotation(b2BodyId bodyId);
// 返回值: 物体的旋转信息

// 设置物体变换
void b2Body_SetTransform(b2BodyId bodyId, b2Vec2 position, b2Rot rotation);
// 参数: bodyId - 物体ID, position - 新位置, rotation - 新旋转
// 作用: 直接设置物体的位置和旋转
```

#### 速度控制
```cpp
// 获取线性速度
b2Vec2 b2Body_GetLinearVelocity(b2BodyId bodyId);
// 返回值: 物体的线性速度向量

// 设置线性速度
void b2Body_SetLinearVelocity(b2BodyId bodyId, b2Vec2 velocity);
// 参数: bodyId - 物体ID, velocity - 速度向量
// 作用: 直接设置物体的移动速度

// 获取角速度
float b2Body_GetAngularVelocity(b2BodyId bodyId);
// 返回值: 物体的角速度 (弧度/秒)

// 设置角速度
void b2Body_SetAngularVelocity(b2BodyId bodyId, float omega);
// 参数: bodyId - 物体ID, omega - 角速度
// 作用: 直接设置物体的旋转速度
```

### 3.6 力与冲量

#### 力的概念
- **力 (Force)**: 持续作用，影响加速度，单位：牛顿 (N)
- **冲量 (Impulse)**: 瞬时作用，直接改变速度，单位：牛顿⋅秒 (N⋅s)
- **扭矩 (Torque)**: 旋转力，影响角加速度，单位：牛顿⋅米 (N⋅m)

#### 施加力
```cpp
// 在质心施加力
b2Vec2 force = {100.0f, 0.0f};  // 100N 向右
b2Body_ApplyForceToCenter(bodyId, force, true);
// 参数: bodyId - 物体ID, force - 力向量, wake - 是否唤醒物体
// 作用: 在物体质心施加持续的力，影响线性加速度

// 在指定点施加力
b2Vec2 force = {50.0f, 0.0f};
b2Vec2 point = {1.0f, 1.0f};  // 世界坐标
b2Body_ApplyForce(bodyId, force, point, true);
// 参数: bodyId - 物体ID, force - 力向量, point - 作用点, wake - 是否唤醒
// 作用: 在指定点施加力，可能产生扭矩

// 施加扭矩
float torque = 10.0f;  // 10 N⋅m 逆时针
b2Body_ApplyTorque(bodyId, torque, true);
// 参数: bodyId - 物体ID, torque - 扭矩, wake - 是否唤醒物体
// 作用: 施加旋转力矩，影响角加速度
```

#### 施加冲量
```cpp
// 在质心施加线性冲量
b2Vec2 impulse = {5.0f, 0.0f};  // 5 N⋅s 向右
b2Body_ApplyLinearImpulseToCenter(bodyId, impulse, true);
// 参数: bodyId - 物体ID, impulse - 冲量向量, wake - 是否唤醒物体
// 作用: 瞬时改变线性速度

// 在指定点施加冲量
b2Vec2 impulse = {3.0f, 0.0f};
b2Vec2 point = {0.0f, 1.0f};  // 世界坐标
b2Body_ApplyLinearImpulse(bodyId, impulse, point, true);
// 参数: bodyId - 物体ID, impulse - 冲量向量, point - 作用点, wake - 是否唤醒
// 作用: 在指定点施加冲量，可能改变线性和角速度

// 施加角冲量
float angularImpulse = 2.0f;  // 2 N⋅m⋅s 逆时针
b2Body_ApplyAngularImpulse(bodyId, angularImpulse, true);
// 参数: bodyId - 物体ID, angularImpulse - 角冲量, wake - 是否唤醒物体
// 作用: 瞬时改变角速度
```

#### 实际应用示例
```cpp
// 跳跃 (向上冲量)
b2Vec2 jumpImpulse = {0.0f, -mass * 5.0f};  // 向上5m/s
b2Body_ApplyLinearImpulseToCenter(bodyId, jumpImpulse, true);

// 推力 (持续力)
b2Vec2 thrustForce = {mass * 10.0f, 0.0f};  // 10m/s²加速度
b2Body_ApplyForceToCenter(bodyId, thrustForce, true);

// 爆炸效果 (径向冲量)
b2Vec2 explosionCenter = {0.0f, 0.0f};
b2Vec2 bodyPos = b2Body_GetPosition(bodyId);
b2Vec2 direction = b2Sub(bodyPos, explosionCenter);
direction = b2Normalize(direction);
b2Vec2 explosionImpulse = b2MulSV(explosionForce, direction);
b2Body_ApplyLinearImpulseToCenter(bodyId, explosionImpulse, true);
```

### 3.7 质量属性

#### 质量数据结构
```cpp
typedef struct b2MassData {
    float mass;        // 质量 (kg)
    b2Vec2 center;     // 质心位置 (本地坐标)
    float rotationalInertia;  // 转动惯量 (kg⋅m²)
} b2MassData;
```

#### 获取质量属性
```cpp
// 获取质量
float mass = b2Body_GetMass(bodyId);
// 返回值: 物体的总质量 (kg)

// 获取转动惯量
float inertia = b2Body_GetInertiaTensor(bodyId);
// 返回值: 物体绕质心的转动惯量 (kg⋅m²)

// 获取完整质量数据
b2MassData massData = b2Body_GetMassData(bodyId);
printf("Mass: %.2f kg\n", massData.mass);
printf("Center: (%.2f, %.2f)\n", massData.center.x, massData.center.y);
printf("Inertia: %.2f kg⋅m²\n", massData.rotationalInertia);

// 获取质心位置
b2Vec2 centerOfMass = b2Body_GetLocalCenterOfMass(bodyId);  // 本地坐标
b2Vec2 worldCenter = b2Body_GetWorldCenterOfMass(bodyId);   // 世界坐标
```

#### 设置质量属性
```cpp
// 重新计算质量 (基于附加的形状)
b2Body_ApplyMassFromShapes(bodyId);
// 作用: 根据所有形状的密度重新计算质量属性

// 手动设置质量数据
b2MassData customMass;
customMass.mass = 10.0f;                    // 10 kg
customMass.center = {0.0f, 0.0f};           // 质心在原点
customMass.rotationalInertia = 5.0f;        // 5 kg⋅m²
b2Body_SetMassData(bodyId, customMass);
// 参数: bodyId - 物体ID, massData - 质量数据
// 作用: 直接设置质量属性，覆盖形状计算的值
```

#### 质量相关计算
```cpp
// 计算所需的力 (F = ma)
float desiredAcceleration = 5.0f;  // m/s²
float mass = b2Body_GetMass(bodyId);
b2Vec2 requiredForce = {mass * desiredAcceleration, 0.0f};

// 计算所需的冲量 (J = m⋅Δv)
b2Vec2 currentVel = b2Body_GetLinearVelocity(bodyId);
b2Vec2 targetVel = {10.0f, 0.0f};
b2Vec2 deltaVel = b2Sub(targetVel, currentVel);
b2Vec2 requiredImpulse = b2MulSV(mass, deltaVel);
```

### 3.8 物体状态

#### 物体类型管理
```cpp
// 获取物体类型
b2BodyType type = b2Body_GetType(bodyId);
switch (type) {
    case b2_staticBody:
        printf("Static body\n");
        break;
    case b2_dynamicBody:
        printf("Dynamic body\n");
        break;
    case b2_kinematicBody:
        printf("Kinematic body\n");
        break;
}

// 设置物体类型
b2Body_SetType(bodyId, b2_kinematicBody);
// 参数: bodyId - 物体ID, type - 新的物体类型
// 作用: 改变物体的类型，会重置速度和力
```

#### 睡眠状态管理
```cpp
// 检查物体是否唤醒
bool isAwake = b2Body_IsAwake(bodyId);
// 返回值: 物体是否处于活跃状态

// 设置睡眠状态
b2Body_SetAwake(bodyId, true);   // 唤醒物体
b2Body_SetAwake(bodyId, false);  // 让物体睡眠
// 参数: bodyId - 物体ID, awake - 是否唤醒
// 作用: 控制物体的睡眠状态，睡眠的物体不参与模拟

// 检查是否允许睡眠
bool canSleep = b2Body_IsSleepEnabled(bodyId);

// 设置睡眠权限
b2Body_EnableSleep(bodyId, true);   // 允许睡眠
b2Body_EnableSleep(bodyId, false);  // 禁止睡眠
```

#### 启用状态管理
```cpp
// 检查物体是否启用
bool isEnabled = b2Body_IsEnabled(bodyId);
// 返回值: 物体是否启用

// 设置启用状态
b2Body_SetEnabled(bodyId, true);   // 启用物体
b2Body_SetEnabled(bodyId, false);  // 禁用物体
// 参数: bodyId - 物体ID, enabled - 是否启用
// 作用: 禁用的物体不参与碰撞和模拟
```

#### 特殊属性
```cpp
// 固定旋转
bool isFixedRotation = b2Body_IsFixedRotation(bodyId);
b2Body_SetFixedRotation(bodyId, true);  // 禁止旋转
// 用途: 角色控制器，防止倾倒

// 子弹模式 (连续碰撞检测)
bool isBullet = b2Body_IsBullet(bodyId);
b2Body_SetBullet(bodyId, true);  // 启用CCD
// 用途: 高速移动的小物体，防止穿透

// 重力缩放
float gravityScale = b2Body_GetGravityScale(bodyId);
b2Body_SetGravityScale(bodyId, 0.5f);  // 一半重力
b2Body_SetGravityScale(bodyId, -1.0f); // 反重力
// 参数: bodyId - 物体ID, scale - 重力缩放因子
```

### 3.9 用户数据

#### 设置和获取用户数据
```cpp
// 设置用户数据
typedef struct GameEntity {
    int entityId;
    int health;
    char name[32];
} GameEntity;

GameEntity* entity = malloc(sizeof(GameEntity));
entity->entityId = 123;
entity->health = 100;
strcpy(entity->name, "Player");

b2Body_SetUserData(bodyId, entity);
// 参数: bodyId - 物体ID, userData - 用户数据指针
// 作用: 关联自定义数据到物体

// 获取用户数据
GameEntity* retrievedEntity = (GameEntity*)b2Body_GetUserData(bodyId);
if (retrievedEntity) {
    printf("Entity: %s, Health: %d\n",
           retrievedEntity->name, retrievedEntity->health);
}

// 清理用户数据 (在销毁物体前)
GameEntity* entity = (GameEntity*)b2Body_GetUserData(bodyId);
if (entity) {
    free(entity);
    b2Body_SetUserData(bodyId, NULL);
}
```

#### 物体状态
```cpp
// 获取物体类型
b2BodyType b2Body_GetType(b2BodyId bodyId);
// 返回值: 物体类型 (static/dynamic/kinematic)

// 设置物体类型
void b2Body_SetType(b2BodyId bodyId, b2BodyType type);
// 参数: bodyId - 物体ID, type - 新的物体类型
// 作用: 改变物体的类型

// 检查物体是否唤醒
bool b2Body_IsAwake(b2BodyId bodyId);
// 返回值: 物体是否处于活跃状态

// 唤醒物体
void b2Body_SetAwake(b2BodyId bodyId, bool awake);
// 参数: bodyId - 物体ID, awake - 是否唤醒
// 作用: 设置物体的睡眠状态
```

---

## 4. Shape (形状)

### 4.1 形状概述

Shape (形状) 定义了物体的几何形状和物理属性：
- 提供碰撞检测的几何信息
- 定义质量分布 (通过密度)
- 设置表面属性 (摩擦、弹性)
- 可以作为传感器使用

#### 形状与物体的关系
- 一个物体可以有多个形状
- 形状必须附加到物体上
- 形状的坐标相对于物体的本地坐标系

### 4.2 形状类型

#### 支持的形状类型
- **b2_circleShape**: 圆形
- **b2_polygonShape**: 凸多边形 (最多8个顶点)
- **b2_capsuleShape**: 胶囊形 (圆角矩形)
- **b2_segmentShape**: 线段

#### 形状定义结构
```cpp
typedef struct b2ShapeDef {
    void* userData;           // 用户数据
    float friction;           // 摩擦系数
    float restitution;        // 恢复系数
    float density;            // 密度
    b2Filter filter;          // 碰撞过滤
    bool isSensor;            // 是否为传感器
    bool enableSensorEvents;  // 启用传感器事件
    bool enableContactEvents; // 启用接触事件
    bool enableHitEvents;     // 启用命中事件
    bool enablePreSolveEvents; // 启用预求解事件
    int customColor;          // 自定义颜色 (调试用)
} b2ShapeDef;
```

### 4.3 圆形 (Circle)

#### 圆形结构
```cpp
typedef struct b2Circle {
    b2Vec2 center;  // 圆心 (本地坐标)
    float radius;   // 半径
} b2Circle;
```

#### 创建圆形
```cpp
// 基础圆形
b2Circle circle;
circle.center = {0.0f, 0.0f};  // 圆心在物体中心
circle.radius = 1.0f;          // 半径1米

b2ShapeDef shapeDef = b2DefaultShapeDef();
shapeDef.density = 1.0f;
shapeDef.friction = 0.3f;
shapeDef.restitution = 0.5f;

b2ShapeId shapeId = b2CreateCircleShape(bodyId, &shapeDef, &circle);

// 偏心圆形
b2Circle offsetCircle;
offsetCircle.center = {0.5f, 0.0f};  // 圆心偏移
offsetCircle.radius = 0.8f;
b2ShapeId offsetShapeId = b2CreateCircleShape(bodyId, &shapeDef, &offsetCircle);
```

#### 圆形应用场景
- 球类游戏
- 轮子
- 角色碰撞体
- 简单的物理对象

### 4.4 多边形 (Polygon)

#### 多边形结构
```cpp
typedef struct b2Polygon {
    b2Vec2 vertices[b2_maxPolygonVertices];  // 顶点数组
    b2Vec2 normals[b2_maxPolygonVertices];   // 法向量数组
    b2Vec2 centroid;                         // 质心
    float radius;                            // 皮肤半径
    int count;                               // 顶点数量
} b2Polygon;
```

#### 创建矩形 (最常用)
```cpp
// 标准矩形
float halfWidth = 2.0f;   // 半宽
float halfHeight = 1.0f;  // 半高
b2Polygon box = b2MakeBox(halfWidth, halfHeight);

b2ShapeDef shapeDef = b2DefaultShapeDef();
shapeDef.density = 1.0f;
b2ShapeId shapeId = b2CreatePolygonShape(bodyId, &shapeDef, &box);

// 偏移矩形
b2Vec2 center = {1.0f, 0.5f};  // 偏移中心
float angle = 0.25f * M_PI;    // 旋转45度
b2Polygon offsetBox = b2MakeOffsetBox(halfWidth, halfHeight, center, b2MakeRot(angle));
b2ShapeId offsetShapeId = b2CreatePolygonShape(bodyId, &shapeDef, &offsetBox);

// 正方形 (特殊矩形)
b2Polygon square = b2MakeSquare(1.0f);  // 边长2米的正方形
```

#### 创建自定义多边形
```cpp
// 三角形
b2Vec2 triangleVertices[3] = {
    {0.0f, 1.0f},   // 顶点
    {-1.0f, -1.0f}, // 左下
    {1.0f, -1.0f}   // 右下
};

b2Hull triangleHull = b2ComputeHull(triangleVertices, 3);
b2Polygon triangle = b2MakePolygon(&triangleHull, 0.0f);
b2ShapeId triangleId = b2CreatePolygonShape(bodyId, &shapeDef, &triangle);

// 六边形
b2Vec2 hexVertices[6];
for (int i = 0; i < 6; ++i) {
    float angle = i * M_PI / 3.0f;  // 60度间隔
    hexVertices[i].x = cosf(angle);
    hexVertices[i].y = sinf(angle);
}

b2Hull hexHull = b2ComputeHull(hexVertices, 6);
b2Polygon hexagon = b2MakePolygon(&hexHull, 0.0f);
b2ShapeId hexId = b2CreatePolygonShape(bodyId, &shapeDef, &hexagon);
```

#### 多边形验证
```cpp
// 验证多边形是否有效
bool isValid = b2ValidateHull(&hull);
if (!isValid) {
    printf("Invalid polygon hull!\n");
    return;
}

// 检查多边形属性
printf("Vertex count: %d\n", polygon.count);
printf("Centroid: (%.2f, %.2f)\n", polygon.centroid.x, polygon.centroid.y);
```

### 4.5 胶囊 (Capsule)

#### 胶囊结构
```cpp
typedef struct b2Capsule {
    b2Vec2 center1;  // 第一个圆心
    b2Vec2 center2;  // 第二个圆心
    float radius;    // 半径
} b2Capsule;
```

#### 创建胶囊
```cpp
// 垂直胶囊
b2Capsule capsule;
capsule.center1 = {0.0f, -1.0f};  // 下圆心
capsule.center2 = {0.0f, 1.0f};   // 上圆心
capsule.radius = 0.5f;            // 半径

b2ShapeDef shapeDef = b2DefaultShapeDef();
shapeDef.density = 1.0f;
b2ShapeId capsuleId = b2CreateCapsuleShape(bodyId, &shapeDef, &capsule);

// 水平胶囊
b2Capsule horizontalCapsule;
horizontalCapsule.center1 = {-2.0f, 0.0f};
horizontalCapsule.center2 = {2.0f, 0.0f};
horizontalCapsule.radius = 0.3f;
```

#### 胶囊应用场景
- 角色控制器 (避免卡边)
- 车辆车身
- 管道、柱子

### 4.6 线段 (Segment)

#### 线段结构
```cpp
typedef struct b2Segment {
    b2Vec2 point1;  // 起点
    b2Vec2 point2;  // 终点
} b2Segment;
```

#### 创建线段
```cpp
// 水平线段
b2Segment segment;
segment.point1 = {-5.0f, 0.0f};  // 起点
segment.point2 = {5.0f, 0.0f};   // 终点

b2ShapeDef shapeDef = b2DefaultShapeDef();
shapeDef.friction = 0.6f;
b2ShapeId segmentId = b2CreateSegmentShape(bodyId, &shapeDef, &segment);

// 斜线段
b2Segment slopeSegment;
slopeSegment.point1 = {0.0f, 0.0f};
slopeSegment.point2 = {10.0f, 3.0f};  // 斜坡
```

#### 线段应用场景
- 地面边界
- 斜坡
- 一维碰撞体
- 传感器线

### 4.7 形状属性

#### 密度 (Density)
密度决定了形状的质量分布：
```cpp
// 设置密度
b2Shape_SetDensity(shapeId, 2.5f);  // 2.5 kg/m²
// 参数: shapeId - 形状ID, density - 密度值
// 作用: 设置形状密度，影响物体质量

// 获取密度
float density = b2Shape_GetDensity(shapeId);

// 常见密度值参考
// 水: 1.0 kg/m²
// 木材: 0.5-0.8 kg/m²
// 石头: 2.5-3.0 kg/m²
// 金属: 7.0-8.0 kg/m²
```

#### 摩擦力 (Friction)
摩擦力影响物体间的滑动：
```cpp
// 设置摩擦力
b2Shape_SetFriction(shapeId, 0.7f);  // 高摩擦
// 参数: shapeId - 形状ID, friction - 摩擦系数 (0-1+)
// 作用: 设置表面摩擦力

// 获取摩擦力
float friction = b2Shape_GetFriction(shapeId);

// 摩擦力组合规则 (两个接触形状)
// 最终摩擦力 = sqrt(friction1 * friction2)

// 常见摩擦系数
// 冰面: 0.02-0.1
// 金属: 0.15-0.25
// 木材: 0.25-0.5
// 橡胶: 0.6-1.0
```

#### 恢复系数 (Restitution)
恢复系数决定碰撞的弹性：
```cpp
// 设置恢复系数
b2Shape_SetRestitution(shapeId, 0.8f);  // 高弹性
// 参数: shapeId - 形状ID, restitution - 恢复系数 (0-1)
// 作用: 设置弹性碰撞系数

// 获取恢复系数
float restitution = b2Shape_GetRestitution(shapeId);

// 恢复系数含义
// 0.0: 完全非弹性 (粘土)
// 0.5: 中等弹性 (木球)
// 0.9: 高弹性 (橡胶球)
// 1.0: 完全弹性 (理想情况)
```

#### 材质组合示例
```cpp
// 创建不同材质的形状
void CreateMaterials(b2BodyId bodyId) {
    b2ShapeDef shapeDef = b2DefaultShapeDef();

    // 金属材质
    shapeDef.density = 7.8f;      // 钢铁密度
    shapeDef.friction = 0.2f;     // 低摩擦
    shapeDef.restitution = 0.1f;  // 低弹性

    // 橡胶材质
    shapeDef.density = 1.2f;      // 橡胶密度
    shapeDef.friction = 0.9f;     // 高摩擦
    shapeDef.restitution = 0.8f;  // 高弹性

    // 冰块材质
    shapeDef.density = 0.9f;      // 冰的密度
    shapeDef.friction = 0.05f;    // 极低摩擦
    shapeDef.restitution = 0.1f;  // 低弹性
}
```

### 4.8 碰撞过滤

#### 过滤器结构
```cpp
typedef struct b2Filter {
    uint32_t categoryBits;  // 类别位
    uint32_t maskBits;      // 掩码位
    int32_t groupIndex;     // 组索引
} b2Filter;
```

#### 基础过滤设置
```cpp
// 设置碰撞过滤
b2Filter filter;
filter.categoryBits = 0x0001;    // 我是什么
filter.maskBits = 0x0002;        // 我与什么碰撞
filter.groupIndex = 0;           // 组索引 (0=不使用)

b2Shape_SetFilter(shapeId, filter);
// 参数: shapeId - 形状ID, filter - 过滤器
// 作用: 设置碰撞过滤规则

// 获取过滤器
b2Filter currentFilter = b2Shape_GetFilter(shapeId);
```

#### 高级过滤示例
```cpp
// 定义碰撞类别
enum CollisionCategory {
    CATEGORY_PLAYER     = 0x0001,  // 玩家
    CATEGORY_ENEMY      = 0x0002,  // 敌人
    CATEGORY_WALL       = 0x0004,  // 墙壁
    CATEGORY_PICKUP     = 0x0008,  // 道具
    CATEGORY_PROJECTILE = 0x0010   // 投射物
};

// 玩家过滤器
b2Filter playerFilter;
playerFilter.categoryBits = CATEGORY_PLAYER;
playerFilter.maskBits = CATEGORY_ENEMY | CATEGORY_WALL | CATEGORY_PICKUP;
playerFilter.groupIndex = 0;

// 敌人过滤器
b2Filter enemyFilter;
enemyFilter.categoryBits = CATEGORY_ENEMY;
enemyFilter.maskBits = CATEGORY_PLAYER | CATEGORY_WALL | CATEGORY_PROJECTILE;
enemyFilter.groupIndex = 0;

// 投射物过滤器 (穿透道具)
b2Filter projectileFilter;
projectileFilter.categoryBits = CATEGORY_PROJECTILE;
projectileFilter.maskBits = CATEGORY_ENEMY | CATEGORY_WALL;  // 不与道具碰撞
projectileFilter.groupIndex = 0;
```

#### 组索引过滤
```cpp
// 正组索引: 同组总是碰撞
b2Filter groupA;
groupA.groupIndex = 1;  // 正值，同组碰撞

// 负组索引: 同组永不碰撞
b2Filter groupB;
groupB.groupIndex = -1;  // 负值，同组不碰撞

// 组索引优先级高于类别/掩码过滤
```

### 4.9 传感器

#### 传感器概念
传感器是特殊的形状，只检测碰撞但不产生物理响应：
- 不阻挡物体移动
- 触发碰撞事件
- 用于触发器、检测区域

#### 创建传感器
```cpp
// 设置为传感器
b2Shape_SetSensor(shapeId, true);
// 参数: shapeId - 形状ID, sensor - 是否为传感器
// 作用: 传感器只检测碰撞，不产生物理响应

// 检查是否为传感器
bool isSensor = b2Shape_IsSensor(shapeId);

// 创建时设置为传感器
b2ShapeDef sensorDef = b2DefaultShapeDef();
sensorDef.isSensor = true;
sensorDef.enableSensorEvents = true;  // 启用传感器事件

b2Circle triggerZone;
triggerZone.center = {0.0f, 0.0f};
triggerZone.radius = 3.0f;
b2ShapeId triggerId = b2CreateCircleShape(bodyId, &sensorDef, &triggerZone);
```

#### 传感器应用示例
```cpp
// 检查点传感器
void CreateCheckpoint(b2BodyId bodyId, b2Vec2 position) {
    b2ShapeDef sensorDef = b2DefaultShapeDef();
    sensorDef.isSensor = true;
    sensorDef.enableSensorEvents = true;

    // 设置过滤器，只检测玩家
    b2Filter filter;
    filter.categoryBits = CATEGORY_CHECKPOINT;
    filter.maskBits = CATEGORY_PLAYER;
    filter.groupIndex = 0;
    sensorDef.filter = filter;

    b2Circle checkpoint;
    checkpoint.center = position;
    checkpoint.radius = 1.0f;

    b2ShapeId checkpointId = b2CreateCircleShape(bodyId, &sensorDef, &checkpoint);
}

// 伤害区域传感器
void CreateDamageZone(b2BodyId bodyId, b2Vec2 center, float width, float height) {
    b2ShapeDef damageDef = b2DefaultShapeDef();
    damageDef.isSensor = true;
    damageDef.enableSensorEvents = true;

    b2Polygon damageZone = b2MakeBox(width/2, height/2);
    b2ShapeId damageId = b2CreatePolygonShape(bodyId, &damageDef, &damageZone);
}
```

#### 形状查询
```cpp
// 获取形状类型
b2ShapeType b2Shape_GetType(b2ShapeId shapeId);
// 返回值: 形状类型枚举

// 获取形状的物体
b2BodyId b2Shape_GetBody(b2ShapeId shapeId);
// 返回值: 拥有此形状的物体ID

// 测试点是否在形状内
bool b2Shape_TestPoint(b2ShapeId shapeId, b2Vec2 point);
// 参数: shapeId - 形状ID, point - 测试点
// 返回值: 点是否在形状内部
```

---

## 6. 碰撞检测

### 6.1 碰撞概述

Box2D提供多种碰撞检测方式：
- **连续碰撞检测**: 自动处理物体间的碰撞
- **射线投射**: 检测射线与形状的交点
- **点测试**: 检查点是否在形状内
- **AABB查询**: 查询轴对齐包围盒内的形状
- **形状投射**: 移动形状的碰撞检测

### 6.2 碰撞回调

#### 碰撞事件类型
```cpp
// 开始接触事件
typedef struct b2ContactBeginTouchEvent {
    b2ShapeId shapeIdA;     // 形状A
    b2ShapeId shapeIdB;     // 形状B
    b2Manifold manifold;    // 接触流形
} b2ContactBeginTouchEvent;

// 结束接触事件
typedef struct b2ContactEndTouchEvent {
    b2ShapeId shapeIdA;     // 形状A
    b2ShapeId shapeIdB;     // 形状B
} b2ContactEndTouchEvent;

// 传感器事件
typedef struct b2SensorBeginTouchEvent {
    b2ShapeId sensorShapeId;   // 传感器形状
    b2ShapeId visitorShapeId;  // 访问者形状
} b2SensorBeginTouchEvent;
```

#### 处理碰撞事件
```cpp
void ProcessCollisionEvents(b2WorldId worldId) {
    b2WorldEvents events = b2World_GetEvents(worldId);

    // 处理开始接触
    for (int i = 0; i < events.beginContactCount; ++i) {
        b2ContactBeginTouchEvent* event = events.beginContactEvents + i;
        OnContactBegin(event->shapeIdA, event->shapeIdB, &event->manifold);
    }

    // 处理结束接触
    for (int i = 0; i < events.endContactCount; ++i) {
        b2ContactEndTouchEvent* event = events.endContactEvents + i;
        OnContactEnd(event->shapeIdA, event->shapeIdB);
    }

    // 处理传感器进入
    for (int i = 0; i < events.sensorBeginCount; ++i) {
        b2SensorBeginTouchEvent* event = events.sensorBeginEvents + i;
        OnSensorEnter(event->sensorShapeId, event->visitorShapeId);
    }

    // 处理传感器离开
    for (int i = 0; i < events.sensorEndCount; ++i) {
        b2SensorEndTouchEvent* event = events.sensorEndEvents + i;
        OnSensorExit(event->sensorShapeId, event->visitorShapeId);
    }
}
```

### 6.3 射线投射

#### 射线投射结构
```cpp
typedef struct b2RaycastInput {
    b2Vec2 origin;      // 射线起点
    b2Vec2 translation; // 射线方向和长度
    float maxFraction;  // 最大分数 (0-1)
} b2RaycastInput;

typedef struct b2RaycastResult {
    b2ShapeId shapeId;  // 命中的形状
    b2Vec2 point;       // 命中点
    b2Vec2 normal;      // 命中点法向量
    float fraction;     // 命中分数 (0-1)
    bool hit;           // 是否命中
} b2RaycastResult;
```

#### 执行射线投射
```cpp
// 单次射线投射
b2RaycastInput rayInput;
rayInput.origin = {0.0f, 0.0f};           // 起点
rayInput.translation = {10.0f, 0.0f};     // 向右10米
rayInput.maxFraction = 1.0f;              // 完整射线

b2RaycastResult result = b2World_CastRay(worldId, &rayInput);
if (result.hit) {
    printf("Hit at: (%.2f, %.2f)\n", result.point.x, result.point.y);
    printf("Normal: (%.2f, %.2f)\n", result.normal.x, result.normal.y);
    printf("Distance: %.2f\n", result.fraction * 10.0f);
}

// 射线投射回调 (获取所有命中)
typedef struct RaycastContext {
    int hitCount;
    b2RaycastResult hits[10];
} RaycastContext;

float RaycastCallback(b2ShapeId shapeId, b2Vec2 point, b2Vec2 normal,
                      float fraction, void* context) {
    RaycastContext* ctx = (RaycastContext*)context;
    if (ctx->hitCount < 10) {
        ctx->hits[ctx->hitCount].shapeId = shapeId;
        ctx->hits[ctx->hitCount].point = point;
        ctx->hits[ctx->hitCount].normal = normal;
        ctx->hits[ctx->hitCount].fraction = fraction;
        ctx->hits[ctx->hitCount].hit = true;
        ctx->hitCount++;
    }
    return 1.0f;  // 继续射线投射
}

RaycastContext context = {0};
b2World_CastRayCallback(worldId, &rayInput, RaycastCallback, &context);
```

#### 射线投射应用
```cpp
// 视线检测
bool HasLineOfSight(b2WorldId worldId, b2Vec2 from, b2Vec2 to) {
    b2RaycastInput rayInput;
    rayInput.origin = from;
    rayInput.translation = b2Sub(to, from);
    rayInput.maxFraction = 1.0f;

    b2RaycastResult result = b2World_CastRay(worldId, &rayInput);
    return !result.hit;  // 没有阻挡物
}

// 地面检测
float GetGroundHeight(b2WorldId worldId, float x) {
    b2RaycastInput rayInput;
    rayInput.origin = {x, 100.0f};        // 从高处开始
    rayInput.translation = {0.0f, -200.0f}; // 向下投射
    rayInput.maxFraction = 1.0f;

    b2RaycastResult result = b2World_CastRay(worldId, &rayInput);
    if (result.hit) {
        return result.point.y;
    }
    return -100.0f;  // 默认地面高度
}
```

### 6.4 形状查询

#### 点测试
```cpp
// 测试点是否在形状内
b2Vec2 testPoint = {5.0f, 3.0f};
bool isInside = b2Shape_TestPoint(shapeId, testPoint);
// 参数: shapeId - 形状ID, point - 测试点
// 返回值: 点是否在形状内部

// 批量点测试
b2Vec2 points[] = {{1,1}, {2,2}, {3,3}};
for (int i = 0; i < 3; ++i) {
    if (b2Shape_TestPoint(shapeId, points[i])) {
        printf("Point %d is inside\n", i);
    }
}
```

#### 形状投射
```cpp
// 形状投射输入
typedef struct b2ShapeCastInput {
    b2Vec2 points[b2_maxPolygonVertices];  // 形状顶点
    int count;                             // 顶点数
    float radius;                          // 半径
    b2Vec2 translation;                    // 移动向量
    float maxFraction;                     // 最大分数
} b2ShapeCastInput;

// 圆形投射
b2ShapeCastInput castInput;
castInput.count = 1;
castInput.points[0] = {0.0f, 0.0f};      // 圆心
castInput.radius = 1.0f;                 // 半径
castInput.translation = {5.0f, 0.0f};    // 向右移动5米
castInput.maxFraction = 1.0f;

b2RaycastResult castResult = b2World_CastShape(worldId, &castInput);
if (castResult.hit) {
    printf("Shape hit at fraction: %.2f\n", castResult.fraction);
}
```

### 6.5 AABB查询

#### AABB结构
```cpp
typedef struct b2AABB {
    b2Vec2 lowerBound;  // 左下角
    b2Vec2 upperBound;  // 右上角
} b2AABB;
```

#### AABB查询
```cpp
// 定义查询区域
b2AABB queryAABB;
queryAABB.lowerBound = {-5.0f, -5.0f};  // 左下角
queryAABB.upperBound = {5.0f, 5.0f};    // 右上角

// 查询回调
typedef struct QueryContext {
    int shapeCount;
    b2ShapeId shapes[100];
} QueryContext;

bool QueryCallback(b2ShapeId shapeId, void* context) {
    QueryContext* ctx = (QueryContext*)context;
    if (ctx->shapeCount < 100) {
        ctx->shapes[ctx->shapeCount] = shapeId;
        ctx->shapeCount++;
    }
    return true;  // 继续查询
}

QueryContext context = {0};
b2World_QueryAABB(worldId, queryAABB, QueryCallback, &context);

printf("Found %d shapes in AABB\n", context.shapeCount);
```

---

## 5. Joint (关节)

### 5.1 关节概述

Joint (关节) 连接两个物体，限制它们的相对运动：
- 创建机械连接
- 限制自由度
- 提供驱动力
- 模拟真实世界的约束

#### 关节的基本概念
- **锚点 (Anchor)**: 关节在物体上的连接点
- **自由度 (DOF)**: 允许的运动方向数量
- **约束 (Constraint)**: 限制的运动类型
- **马达 (Motor)**: 提供主动驱动力
- **限制 (Limit)**: 运动范围的边界

### 5.2 关节类型

#### 支持的关节类型
- **b2_distanceJoint**: 距离关节 - 保持固定距离
- **b2_revoluteJoint**: 旋转关节 - 绕点旋转
- **b2_prismaticJoint**: 平移关节 - 直线滑动
- **b2_weldJoint**: 焊接关节 - 完全固定
- **b2_wheelJoint**: 轮子关节 - 车轮悬挂
- **b2_pulleyJoint**: 滑轮关节 - 滑轮系统
- **b2_gearJoint**: 齿轮关节 - 齿轮传动
- **b2_motorJoint**: 马达关节 - 位置/角度驱动

#### 关节定义基础结构
```cpp
typedef struct b2JointDef {
    b2BodyId bodyIdA;        // 物体A
    b2BodyId bodyIdB;        // 物体B
    b2Transform localFrameA; // 物体A上的本地坐标系
    b2Transform localFrameB; // 物体B上的本地坐标系
    bool collideConnected;   // 连接的物体是否碰撞
    void* userData;          // 用户数据
} b2JointDef;
```

### 5.3 旋转关节 (Revolute Joint)

#### 旋转关节概述
旋转关节允许两个物体绕共同点旋转：
- 1个自由度 (旋转)
- 可设置角度限制
- 可添加马达驱动
- 常用于门、轮子、摆锤

#### 旋转关节定义
```cpp
typedef struct b2RevoluteJointDef {
    b2JointDef base;           // 基础关节定义
    float referenceAngle;      // 参考角度
    bool enableLimit;          // 启用角度限制
    float lowerAngle;          // 下限角度
    float upperAngle;          // 上限角度
    bool enableMotor;          // 启用马达
    float motorSpeed;          // 马达速度
    float maxMotorTorque;      // 最大马达扭矩
} b2RevoluteJointDef;
```

#### 创建旋转关节
```cpp
// 基础旋转关节
b2RevoluteJointDef jointDef = b2DefaultRevoluteJointDef();
jointDef.base.bodyIdA = bodyA;
jointDef.base.bodyIdB = bodyB;
jointDef.base.localFrameA.p = {1.0f, 0.0f};  // bodyA上的锚点
jointDef.base.localFrameB.p = {-1.0f, 0.0f}; // bodyB上的锚点
jointDef.base.collideConnected = false;      // 连接物体不碰撞

b2JointId jointId = b2CreateRevoluteJoint(worldId, &jointDef);

// 带限制的旋转关节
b2RevoluteJointDef limitedDef = b2DefaultRevoluteJointDef();
limitedDef.base.bodyIdA = bodyA;
limitedDef.base.bodyIdB = bodyB;
limitedDef.base.localFrameA.p = {0.0f, 0.0f};
limitedDef.base.localFrameB.p = {0.0f, 0.0f};
limitedDef.enableLimit = true;
limitedDef.lowerAngle = -M_PI / 4;  // -45度
limitedDef.upperAngle = M_PI / 4;   // +45度

b2JointId limitedJointId = b2CreateRevoluteJoint(worldId, &limitedDef);

// 带马达的旋转关节
b2RevoluteJointDef motorDef = b2DefaultRevoluteJointDef();
motorDef.base.bodyIdA = bodyA;
motorDef.base.bodyIdB = bodyB;
motorDef.base.localFrameA.p = {0.0f, 0.0f};
motorDef.base.localFrameB.p = {0.0f, 0.0f};
motorDef.enableMotor = true;
motorDef.motorSpeed = 2.0f;      // 2弧度/秒
motorDef.maxMotorTorque = 10.0f; // 10牛顿米

b2JointId motorJointId = b2CreateRevoluteJoint(worldId, &motorDef);
```

#### 旋转关节控制
```cpp
// 角度限制控制
b2RevoluteJoint_EnableLimit(jointId, true);
b2RevoluteJoint_SetLimits(jointId, -M_PI/2, M_PI/2);  // ±90度

// 获取角度限制
bool limitEnabled = b2RevoluteJoint_IsLimitEnabled(jointId);
float lowerLimit = b2RevoluteJoint_GetLowerLimit(jointId);
float upperLimit = b2RevoluteJoint_GetUpperLimit(jointId);

// 马达控制
b2RevoluteJoint_EnableMotor(jointId, true);
b2RevoluteJoint_SetMotorSpeed(jointId, 1.5f);    // 设置速度
b2RevoluteJoint_SetMaxMotorTorque(jointId, 20.0f); // 设置最大扭矩

// 获取马达状态
bool motorEnabled = b2RevoluteJoint_IsMotorEnabled(jointId);
float motorSpeed = b2RevoluteJoint_GetMotorSpeed(jointId);
float motorTorque = b2RevoluteJoint_GetMotorTorque(jointId);
```

#### 获取关节状态
```cpp
// 获取当前角度
float currentAngle = b2RevoluteJoint_GetAngle(jointId);
// 返回值: 当前相对角度 (弧度)

// 获取角速度
float angularSpeed = b2RevoluteJoint_GetAngularSpeed(jointId);
// 返回值: 当前角速度 (弧度/秒)

// 获取马达扭矩
float motorTorque = b2RevoluteJoint_GetMotorTorque(jointId);
// 返回值: 当前马达输出扭矩
```

#### 旋转关节应用示例
```cpp
// 门关节
b2JointId CreateDoorJoint(b2WorldId worldId, b2BodyId wall, b2BodyId door) {
    b2RevoluteJointDef doorDef = b2DefaultRevoluteJointDef();
    doorDef.base.bodyIdA = wall;
    doorDef.base.bodyIdB = door;
    doorDef.base.localFrameA.p = {0.0f, 0.0f};    // 墙上的铰链点
    doorDef.base.localFrameB.p = {-1.0f, 0.0f};   // 门的铰链点
    doorDef.enableLimit = true;
    doorDef.lowerAngle = 0.0f;        // 关闭位置
    doorDef.upperAngle = M_PI / 2;    // 90度开启

    return b2CreateRevoluteJoint(worldId, &doorDef);
}

// 车轮关节
b2JointId CreateWheelJoint(b2WorldId worldId, b2BodyId chassis, b2BodyId wheel) {
    b2RevoluteJointDef wheelDef = b2DefaultRevoluteJointDef();
    wheelDef.base.bodyIdA = chassis;
    wheelDef.base.bodyIdB = wheel;
    wheelDef.base.localFrameA.p = {2.0f, -1.0f};  // 车身上的轮轴
    wheelDef.base.localFrameB.p = {0.0f, 0.0f};   // 轮子中心
    wheelDef.enableMotor = true;
    wheelDef.maxMotorTorque = 100.0f;  // 驱动扭矩

    return b2CreateRevoluteJoint(worldId, &wheelDef);
}

// 摆锤关节
b2JointId CreatePendulumJoint(b2WorldId worldId, b2BodyId anchor, b2BodyId pendulum) {
    b2RevoluteJointDef pendulumDef = b2DefaultRevoluteJointDef();
    pendulumDef.base.bodyIdA = anchor;
    pendulumDef.base.bodyIdB = pendulum;
    pendulumDef.base.localFrameA.p = {0.0f, 0.0f};
    pendulumDef.base.localFrameB.p = {0.0f, 2.0f};  // 摆锤顶部
    // 无限制，自由摆动

    return b2CreateRevoluteJoint(worldId, &pendulumDef);
}
```

### 5.4 平移关节 (Prismatic Joint)

#### 平移关节概述
平移关节允许两个物体沿直线相对滑动：
- 1个自由度 (直线运动)
- 可设置移动限制
- 可添加线性马达
- 常用于活塞、滑块、电梯

#### 平移关节定义
```cpp
typedef struct b2PrismaticJointDef {
    b2JointDef base;           // 基础关节定义
    b2Vec2 localAxisA;         // 物体A上的滑动轴
    float referenceAngle;      // 参考角度
    bool enableLimit;          // 启用位移限制
    float lowerTranslation;    // 下限位移
    float upperTranslation;    // 上限位移
    bool enableMotor;          // 启用马达
    float motorSpeed;          // 马达速度
    float maxMotorForce;       // 最大马达力
} b2PrismaticJointDef;
```

#### 创建平移关节
```cpp
// 垂直滑块
b2PrismaticJointDef sliderDef = b2DefaultPrismaticJointDef();
sliderDef.base.bodyIdA = bodyA;
sliderDef.base.bodyIdB = bodyB;
sliderDef.base.localFrameA.p = {0.0f, 0.0f};
sliderDef.base.localFrameB.p = {0.0f, 0.0f};
sliderDef.localAxisA = {0.0f, 1.0f};      // Y轴方向滑动
sliderDef.enableLimit = true;
sliderDef.lowerTranslation = -2.0f;       // 向下2米
sliderDef.upperTranslation = 2.0f;        // 向上2米

b2JointId sliderId = b2CreatePrismaticJoint(worldId, &sliderDef);

// 带马达的活塞
b2PrismaticJointDef pistonDef = b2DefaultPrismaticJointDef();
pistonDef.base.bodyIdA = bodyA;
pistonDef.base.bodyIdB = bodyB;
pistonDef.base.localFrameA.p = {0.0f, 0.0f};
pistonDef.base.localFrameB.p = {0.0f, 0.0f};
pistonDef.localAxisA = {1.0f, 0.0f};      // X轴方向
pistonDef.enableMotor = true;
pistonDef.motorSpeed = 1.0f;              // 1米/秒
pistonDef.maxMotorForce = 100.0f;         // 100牛顿

b2JointId pistonId = b2CreatePrismaticJoint(worldId, &pistonDef);
```

### 5.5 距离关节 (Distance Joint)

#### 距离关节概述
距离关节保持两个物体间的固定距离：
- 类似弹簧连接
- 可设置弹簧参数
- 允许旋转自由度
- 常用于绳索、弹簧、悬挂

#### 距离关节定义
```cpp
typedef struct b2DistanceJointDef {
    b2JointDef base;           // 基础关节定义
    float length;              // 目标距离
    bool enableSpring;         // 启用弹簧
    float hertz;               // 弹簧频率
    float dampingRatio;        // 阻尼比
    bool enableLimit;          // 启用长度限制
    float minLength;           // 最小长度
    float maxLength;           // 最大长度
} b2DistanceJointDef;
```

#### 创建距离关节
```cpp
// 刚性距离关节
b2DistanceJointDef rigidDef = b2DefaultDistanceJointDef();
rigidDef.base.bodyIdA = bodyA;
rigidDef.base.bodyIdB = bodyB;
rigidDef.base.localFrameA.p = {0.0f, 0.0f};
rigidDef.base.localFrameB.p = {0.0f, 0.0f};
rigidDef.length = 5.0f;               // 5米距离
rigidDef.enableSpring = false;        // 刚性连接

b2JointId rigidId = b2CreateDistanceJoint(worldId, &rigidDef);

// 弹簧距离关节
b2DistanceJointDef springDef = b2DefaultDistanceJointDef();
springDef.base.bodyIdA = bodyA;
springDef.base.bodyIdB = bodyB;
springDef.base.localFrameA.p = {0.0f, 0.0f};
springDef.base.localFrameB.p = {0.0f, 0.0f};
springDef.length = 3.0f;              // 3米自然长度
springDef.enableSpring = true;        // 启用弹簧
springDef.hertz = 4.0f;               // 4Hz频率
springDef.dampingRatio = 0.5f;        // 50%阻尼

b2JointId springId = b2CreateDistanceJoint(worldId, &springDef);
```

### 5.6 焊接关节 (Weld Joint)

#### 焊接关节概述
焊接关节完全固定两个物体：
- 0个自由度
- 刚性连接
- 可设置弹性参数
- 常用于复合物体、固定连接

#### 焊接关节定义
```cpp
typedef struct b2WeldJointDef {
    b2JointDef base;           // 基础关节定义
    float referenceAngle;      // 参考角度
    float linearHertz;         // 线性频率
    float linearDampingRatio;  // 线性阻尼比
    float angularHertz;        // 角度频率
    float angularDampingRatio; // 角度阻尼比
} b2WeldJointDef;
```

#### 创建焊接关节
```cpp
// 刚性焊接
b2WeldJointDef weldDef = b2DefaultWeldJointDef();
weldDef.base.bodyIdA = bodyA;
weldDef.base.bodyIdB = bodyB;
weldDef.base.localFrameA.p = {1.0f, 0.0f};
weldDef.base.localFrameB.p = {-1.0f, 0.0f};
weldDef.referenceAngle = 0.0f;        // 保持当前角度

b2JointId weldId = b2CreateWeldJoint(worldId, &weldDef);

// 柔性焊接 (有弹性)
b2WeldJointDef flexWeldDef = b2DefaultWeldJointDef();
flexWeldDef.base.bodyIdA = bodyA;
flexWeldDef.base.bodyIdB = bodyB;
flexWeldDef.base.localFrameA.p = {0.0f, 0.0f};
flexWeldDef.base.localFrameB.p = {0.0f, 0.0f};
flexWeldDef.linearHertz = 8.0f;       // 线性弹性
flexWeldDef.linearDampingRatio = 0.7f;
flexWeldDef.angularHertz = 8.0f;      // 角度弹性
flexWeldDef.angularDampingRatio = 0.7f;

b2JointId flexWeldId = b2CreateWeldJoint(worldId, &flexWeldDef);
```

### 5.7 轮子关节 (Wheel Joint)

#### 轮子关节概述
轮子关节结合了平移和旋转，专为车轮设计：
- 垂直方向有弹簧悬挂
- 水平方向可自由滑动
- 可添加马达驱动
- 常用于车辆系统

#### 轮子关节定义
```cpp
typedef struct b2WheelJointDef {
    b2JointDef base;           // 基础关节定义
    b2Vec2 localAxisA;         // 物体A上的悬挂轴
    bool enableSpring;         // 启用弹簧
    float hertz;               // 弹簧频率
    float dampingRatio;        // 阻尼比
    bool enableLimit;          // 启用旋转限制
    float lowerAngle;          // 下限角度
    float upperAngle;          // 上限角度
    bool enableMotor;          // 启用马达
    float motorSpeed;          // 马达速度
    float maxMotorTorque;      // 最大马达扭矩
} b2WheelJointDef;
```

#### 创建轮子关节
```cpp
// 车轮悬挂
b2WheelJointDef wheelDef = b2DefaultWheelJointDef();
wheelDef.base.bodyIdA = chassis;      // 车身
wheelDef.base.bodyIdB = wheel;        // 轮子
wheelDef.base.localFrameA.p = {2.0f, -1.0f};  // 车身上的悬挂点
wheelDef.base.localFrameB.p = {0.0f, 0.0f};   // 轮子中心
wheelDef.localAxisA = {0.0f, 1.0f};   // 垂直悬挂轴
wheelDef.enableSpring = true;
wheelDef.hertz = 4.0f;                // 悬挂频率
wheelDef.dampingRatio = 0.7f;         // 悬挂阻尼
wheelDef.enableMotor = true;
wheelDef.maxMotorTorque = 80.0f;      // 驱动扭矩

b2JointId wheelId = b2CreateWheelJoint(worldId, &wheelDef);
```

### 5.8 马达关节 (Motor Joint)

#### 马达关节概述
马达关节提供位置和角度的直接控制：
- 不使用锚点连接
- 直接控制相对位置/角度
- 可设置最大力和扭矩
- 常用于角色控制、平台移动

#### 马达关节定义
```cpp
typedef struct b2MotorJointDef {
    b2JointDef base;           // 基础关节定义
    b2Vec2 linearOffset;       // 线性偏移
    float angularOffset;       // 角度偏移
    float maxForce;            // 最大力
    float maxTorque;           // 最大扭矩
    float correctionFactor;    // 修正因子
} b2MotorJointDef;
```

#### 创建马达关节
```cpp
// 位置控制马达
b2MotorJointDef motorDef = b2DefaultMotorJointDef();
motorDef.base.bodyIdA = bodyA;
motorDef.base.bodyIdB = bodyB;
motorDef.linearOffset = {2.0f, 1.0f}; // 目标相对位置
motorDef.angularOffset = 0.0f;        // 目标相对角度
motorDef.maxForce = 100.0f;           // 最大驱动力
motorDef.maxTorque = 50.0f;           // 最大驱动扭矩
motorDef.correctionFactor = 0.3f;     // 修正速度

b2JointId motorId = b2CreateMotorJoint(worldId, &motorDef);
```

### 5.9 通用关节函数

#### 关节管理
```cpp
// 销毁关节
b2DestroyJoint(jointId);

// 获取关节类型
b2JointType type = b2Joint_GetType(jointId);
switch (type) {
    case b2_distanceJoint:
        printf("Distance joint\n");
        break;
    case b2_revoluteJoint:
        printf("Revolute joint\n");
        break;
    // ... 其他类型
}

// 获取连接的物体
b2BodyId bodyA = b2Joint_GetBodyA(jointId);
b2BodyId bodyB = b2Joint_GetBodyB(jointId);

// 获取/设置用户数据
void* userData = b2Joint_GetUserData(jointId);
b2Joint_SetUserData(jointId, myData);

// 检查关节是否有效
bool isValid = B2_IS_NON_NULL(jointId);
```

#### 关节约束力
```cpp
// 获取约束力 (仅在Step后有效)
b2Vec2 constraintForce = b2Joint_GetConstraintForce(jointId);
float constraintTorque = b2Joint_GetConstraintTorque(jointId);

// 用于调试和分析关节受力情况
printf("Constraint force: (%.2f, %.2f) N\n",
       constraintForce.x, constraintForce.y);
printf("Constraint torque: %.2f N⋅m\n", constraintTorque);
```

### 通用关节函数
```cpp
// 销毁关节
void b2DestroyJoint(b2JointId jointId);

// 获取关节类型
b2JointType b2Joint_GetType(b2JointId jointId);
// 返回值: 关节类型枚举

// 获取连接的物体
b2BodyId b2Joint_GetBodyA(b2JointId jointId);
b2BodyId b2Joint_GetBodyB(b2JointId jointId);
// 返回值: 连接的物体ID
```

---

## 8. 数学工具

### 8.1 向量操作

#### 基础向量结构
```cpp
typedef struct b2Vec2 {
    float x, y;
} b2Vec2;

// 向量常量
#define b2Vec2_zero {0.0f, 0.0f}
```

#### 向量创建和基础操作
```cpp
// 创建向量
b2Vec2 v1 = {3.0f, 4.0f};
b2Vec2 v2 = {1.0f, 2.0f};

// 向量加法
b2Vec2 b2Add(b2Vec2 a, b2Vec2 b);
b2Vec2 sum = b2Add(v1, v2);  // {4.0f, 6.0f}

// 向量减法
b2Vec2 b2Sub(b2Vec2 a, b2Vec2 b);
b2Vec2 diff = b2Sub(v1, v2);  // {2.0f, 2.0f}

// 标量乘法
b2Vec2 b2MulSV(float s, b2Vec2 v);
b2Vec2 scaled = b2MulSV(2.0f, v1);  // {6.0f, 8.0f}

// 向量取负
b2Vec2 b2Neg(b2Vec2 v);
b2Vec2 negated = b2Neg(v1);  // {-3.0f, -4.0f}
```

#### 向量长度和距离
```cpp
// 向量长度
float b2Length(b2Vec2 v);
float length = b2Length(v1);  // 5.0f (3²+4²的平方根)

// 向量长度平方 (避免开方运算)
float b2LengthSquared(b2Vec2 v);
float lengthSq = b2LengthSquared(v1);  // 25.0f

// 两点间距离
float b2Distance(b2Vec2 a, b2Vec2 b);
float dist = b2Distance(v1, v2);

// 距离平方
float b2DistanceSquared(b2Vec2 a, b2Vec2 b);
float distSq = b2DistanceSquared(v1, v2);
```

#### 向量归一化
```cpp
// 向量归一化
b2Vec2 b2Normalize(b2Vec2 v);
b2Vec2 unit = b2Normalize(v1);  // {0.6f, 0.8f}

// 安全归一化 (处理零向量)
b2Vec2 SafeNormalize(b2Vec2 v) {
    float length = b2Length(v);
    if (length < 1e-6f) {
        return (b2Vec2){1.0f, 0.0f};  // 默认方向
    }
    return b2MulSV(1.0f / length, v);
}

// 获取单位向量 (指定方向)
b2Vec2 GetUnitVector(float angle) {
    return (b2Vec2){cosf(angle), sinf(angle)};
}
```

#### 向量点积和叉积
```cpp
// 点积 (数量积)
float b2Dot(b2Vec2 a, b2Vec2 b);
float dot = b2Dot(v1, v2);  // 3*1 + 4*2 = 11

// 叉积 (向量积的Z分量)
float b2Cross(b2Vec2 a, b2Vec2 b);
float cross = b2Cross(v1, v2);  // 3*2 - 4*1 = 2

// 向量与标量的叉积
b2Vec2 b2CrossSV(float s, b2Vec2 v);  // 垂直向量
b2Vec2 b2CrossVS(b2Vec2 v, float s);  // 垂直向量

// 应用示例
b2Vec2 perpendicular = b2CrossSV(1.0f, v1);  // 垂直向量
```

### 8.2 旋转操作

#### 旋转结构
```cpp
typedef struct b2Rot {
    float c, s;  // cos(angle), sin(angle)
} b2Rot;
```

#### 旋转创建和转换
```cpp
// 从角度创建旋转
b2Rot b2MakeRot(float angle);
b2Rot rot = b2MakeRot(M_PI / 4);  // 45度

// 获取角度
float b2Rot_GetAngle(b2Rot q);
float angle = b2Rot_GetAngle(rot);  // M_PI / 4

// 单位旋转 (0度)
b2Rot identity = b2Rot_identity;  // {1.0f, 0.0f}
```

#### 旋转运算
```cpp
// 旋转合成
b2Rot b2MulRot(b2Rot q, b2Rot r);
b2Rot combined = b2MulRot(rot1, rot2);

// 旋转逆运算
b2Rot b2InvMulRot(b2Rot q, b2Rot r);
b2Rot relative = b2InvMulRot(rot1, rot2);

// 旋转向量
b2Vec2 b2RotateVector(b2Rot q, b2Vec2 v);
b2Vec2 rotated = b2RotateVector(rot, v1);

// 逆旋转向量
b2Vec2 b2InvRotateVector(b2Rot q, b2Vec2 v);
b2Vec2 unrotated = b2InvRotateVector(rot, rotated);
```

### 8.3 变换操作

#### 变换结构
```cpp
typedef struct b2Transform {
    b2Vec2 p;  // 位置
    b2Rot q;   // 旋转
} b2Transform;
```

#### 变换创建和操作
```cpp
// 创建变换
b2Transform transform;
transform.p = {5.0f, 3.0f};           // 位置
transform.q = b2MakeRot(M_PI / 6);    // 30度旋转

// 变换点
b2Vec2 b2TransformPoint(b2Transform t, b2Vec2 p);
b2Vec2 worldPoint = b2TransformPoint(transform, localPoint);

// 逆变换点
b2Vec2 b2InvTransformPoint(b2Transform t, b2Vec2 p);
b2Vec2 localPoint = b2InvTransformPoint(transform, worldPoint);

// 变换合成
b2Transform b2MulTransforms(b2Transform A, b2Transform B);
b2Transform combined = b2MulTransforms(parentTransform, childTransform);
```

### 8.4 几何计算

#### 角度工具
```cpp
// 角度标准化 (-π 到 π)
float NormalizeAngle(float angle) {
    while (angle > M_PI) angle -= 2.0f * M_PI;
    while (angle < -M_PI) angle += 2.0f * M_PI;
    return angle;
}

// 角度差值
float AngleDifference(float a, float b) {
    return NormalizeAngle(a - b);
}

// 角度插值
float LerpAngle(float a, float b, float t) {
    float diff = AngleDifference(b, a);
    return a + diff * t;
}
```

#### 插值函数
```cpp
// 线性插值
float Lerp(float a, float b, float t) {
    return a + (b - a) * t;
}

// 向量插值
b2Vec2 LerpVec2(b2Vec2 a, b2Vec2 b, float t) {
    return (b2Vec2){
        Lerp(a.x, b.x, t),
        Lerp(a.y, b.y, t)
    };
}

// 平滑插值 (smoothstep)
float SmoothStep(float t) {
    return t * t * (3.0f - 2.0f * t);
}
```

#### 几何查询
```cpp
// 点到线段的距离
float PointToSegmentDistance(b2Vec2 point, b2Vec2 a, b2Vec2 b) {
    b2Vec2 ab = b2Sub(b, a);
    b2Vec2 ap = b2Sub(point, a);

    float ab_length_sq = b2LengthSquared(ab);
    if (ab_length_sq == 0.0f) {
        return b2Distance(point, a);
    }

    float t = b2Dot(ap, ab) / ab_length_sq;
    t = b2ClampFloat(t, 0.0f, 1.0f);

    b2Vec2 projection = b2Add(a, b2MulSV(t, ab));
    return b2Distance(point, projection);
}

// 点在多边形内测试
bool PointInPolygon(b2Vec2 point, b2Vec2* vertices, int count) {
    bool inside = false;
    for (int i = 0, j = count - 1; i < count; j = i++) {
        if (((vertices[i].y > point.y) != (vertices[j].y > point.y)) &&
            (point.x < (vertices[j].x - vertices[i].x) *
             (point.y - vertices[i].y) / (vertices[j].y - vertices[i].y) + vertices[i].x)) {
            inside = !inside;
        }
    }
    return inside;
}
```

---

## 7. ID系统

### 7.1 ID概述

Box2D 3.0使用ID系统管理所有对象，提供：
- **类型安全**: 不同类型的ID不能混用
- **内存安全**: 自动检测失效的ID
- **版本控制**: 防止使用已销毁对象的ID
- **调试友好**: 便于追踪对象生命周期

### 7.2 ID类型

#### 核心ID类型
```cpp
typedef struct b2WorldId { uint64_t index1; uint16_t revision; } b2WorldId;
typedef struct b2BodyId { uint32_t index1; uint16_t revision; } b2BodyId;
typedef struct b2ShapeId { uint32_t index1; uint16_t revision; } b2ShapeId;
typedef struct b2JointId { uint32_t index1; uint16_t revision; } b2JointId;
```

#### ID结构说明
- **index1**: 对象在数组中的索引
- **revision**: 版本号，防止使用已销毁对象的ID

### 7.3 ID验证

#### 基础验证函数
```cpp
// 检查ID是否有效 (非空)
bool B2_IS_NON_NULL(b2WorldId id);
bool B2_IS_NON_NULL(b2BodyId id);
bool B2_IS_NON_NULL(b2ShapeId id);
bool B2_IS_NON_NULL(b2JointId id);

// 检查ID是否为空
bool B2_IS_NULL(b2WorldId id);
bool B2_IS_NULL(b2BodyId id);
bool B2_IS_NULL(b2ShapeId id);
bool B2_IS_NULL(b2JointId id);
```

#### 安全的ID使用模式
```cpp
// 安全使用物体ID
void SafeUseBody(b2BodyId bodyId) {
    if (B2_IS_NULL(bodyId)) {
        printf("Error: Invalid body ID\n");
        return;
    }

    // 使用物体
    b2Vec2 position = b2Body_GetPosition(bodyId);
    printf("Body position: (%.2f, %.2f)\n", position.x, position.y);
}

// 安全销毁对象
void SafeDestroyBody(b2BodyId* bodyId) {
    if (B2_IS_NON_NULL(*bodyId)) {
        b2DestroyBody(*bodyId);
        *bodyId = b2_nullBodyId;  // 清空ID
    }
}
```

#### ID比较
```cpp
// ID相等比较
bool b2Body_IsEqual(b2BodyId a, b2BodyId b);
bool b2Shape_IsEqual(b2ShapeId a, b2ShapeId b);
bool b2Joint_IsEqual(b2JointId a, b2JointId b);

// 使用示例
if (b2Body_IsEqual(bodyId1, bodyId2)) {
    printf("Same body\n");
}
```

### 7.4 ID生命周期

#### ID创建和销毁
```cpp
// ID在对象创建时自动生成
b2BodyId bodyId = b2CreateBody(worldId, &bodyDef);  // 创建时获得ID
b2ShapeId shapeId = b2CreateCircleShape(bodyId, &shapeDef, &circle);

// ID在对象销毁时自动失效
b2DestroyBody(bodyId);  // bodyId和所有相关shapeId都失效
```

#### ID失效检测
```cpp
// 检测ID是否仍然有效
bool IsBodyValid(b2BodyId bodyId) {
    if (B2_IS_NULL(bodyId)) {
        return false;
    }

    // 尝试获取物体类型，如果失败说明ID已失效
    b2BodyType type = b2Body_GetType(bodyId);
    return true;  // 如果没有异常，ID有效
}
```

#### ID管理最佳实践
```cpp
// 1. 使用结构体管理相关ID
typedef struct GameObject {
    b2BodyId bodyId;
    b2ShapeId shapeIds[4];  // 最多4个形状
    int shapeCount;
    bool isValid;
} GameObject;

// 2. 创建对象
GameObject* CreateGameObject(b2WorldId worldId) {
    GameObject* obj = malloc(sizeof(GameObject));

    // 创建物体
    b2BodyDef bodyDef = b2DefaultBodyDef();
    obj->bodyId = b2CreateBody(worldId, &bodyDef);

    // 创建形状
    b2ShapeDef shapeDef = b2DefaultShapeDef();
    b2Circle circle = {{0,0}, 1.0f};
    obj->shapeIds[0] = b2CreateCircleShape(obj->bodyId, &shapeDef, &circle);
    obj->shapeCount = 1;
    obj->isValid = true;

    return obj;
}

// 3. 销毁对象
void DestroyGameObject(GameObject* obj) {
    if (obj && obj->isValid) {
        // 销毁物体会自动销毁所有形状
        if (B2_IS_NON_NULL(obj->bodyId)) {
            b2DestroyBody(obj->bodyId);
        }
        obj->isValid = false;
    }
    free(obj);
}

// 4. 检查对象有效性
bool IsGameObjectValid(GameObject* obj) {
    return obj && obj->isValid && B2_IS_NON_NULL(obj->bodyId);
}
```

#### 常见ID错误和解决方案
```cpp
// 错误1: 使用已销毁的ID
b2BodyId bodyId = b2CreateBody(worldId, &bodyDef);
b2DestroyBody(bodyId);
// b2Body_GetPosition(bodyId);  // 错误！ID已失效

// 解决方案: 销毁后清空ID
b2DestroyBody(bodyId);
bodyId = b2_nullBodyId;

// 错误2: 忘记检查ID有效性
void MoveBody(b2BodyId bodyId, b2Vec2 velocity) {
    // b2Body_SetLinearVelocity(bodyId, velocity);  // 可能崩溃

    // 正确做法
    if (B2_IS_NON_NULL(bodyId)) {
        b2Body_SetLinearVelocity(bodyId, velocity);
    }
}

// 错误3: ID类型混用
b2BodyId bodyId = b2CreateBody(worldId, &bodyDef);
// b2Shape_GetDensity(bodyId);  // 编译错误！类型不匹配
```

---

## 10. 性能优化

### 10.1 性能概述

Box2D性能优化的关键领域：
- **物体数量管理**: 减少不必要的物体
- **碰撞优化**: 合理使用碰撞过滤
- **睡眠机制**: 利用物体睡眠节省计算
- **时间步长**: 选择合适的模拟精度
- **内存管理**: 避免频繁创建/销毁对象

### 10.2 内存管理

#### 对象池模式
```cpp
// 物体对象池
typedef struct BodyPool {
    b2BodyId bodies[1000];
    bool used[1000];
    int capacity;
    int count;
} BodyPool;

BodyPool* CreateBodyPool() {
    BodyPool* pool = malloc(sizeof(BodyPool));
    pool->capacity = 1000;
    pool->count = 0;
    memset(pool->used, false, sizeof(pool->used));
    return pool;
}

b2BodyId AcquireBody(BodyPool* pool, b2WorldId worldId, b2BodyDef* bodyDef) {
    for (int i = 0; i < pool->capacity; ++i) {
        if (!pool->used[i]) {
            pool->bodies[i] = b2CreateBody(worldId, bodyDef);
            pool->used[i] = true;
            pool->count++;
            return pool->bodies[i];
        }
    }
    return b2_nullBodyId;  // 池已满
}

void ReleaseBody(BodyPool* pool, b2BodyId bodyId) {
    for (int i = 0; i < pool->capacity; ++i) {
        if (pool->used[i] && b2Body_IsEqual(pool->bodies[i], bodyId)) {
            b2DestroyBody(bodyId);
            pool->used[i] = false;
            pool->count--;
            break;
        }
    }
}
```

#### 批量操作
```cpp
// 批量创建物体
void CreateBulletHell(b2WorldId worldId, int bulletCount) {
    b2BodyDef bulletDef = b2DefaultBodyDef();
    bulletDef.type = b2_dynamicBody;
    bulletDef.isBullet = true;  // 启用CCD

    b2ShapeDef shapeDef = b2DefaultShapeDef();
    shapeDef.density = 0.1f;

    b2Circle bulletShape = {{0,0}, 0.1f};

    for (int i = 0; i < bulletCount; ++i) {
        bulletDef.position.x = (float)(i % 10);
        bulletDef.position.y = (float)(i / 10);

        b2BodyId bulletId = b2CreateBody(worldId, &bulletDef);
        b2CreateCircleShape(bulletId, &shapeDef, &bulletShape);
    }
}
```

### 10.3 计算优化

#### 碰撞过滤优化
```cpp
// 分层碰撞系统
enum CollisionLayers {
    LAYER_STATIC    = 0x0001,  // 静态环境
    LAYER_PLAYER    = 0x0002,  // 玩家
    LAYER_ENEMY     = 0x0004,  // 敌人
    LAYER_PROJECTILE = 0x0008, // 投射物
    LAYER_PICKUP    = 0x0010,  // 道具
    LAYER_EFFECT    = 0x0020   // 特效 (传感器)
};

// 优化的过滤器设置
b2Filter CreateOptimizedFilter(uint32_t category, uint32_t mask) {
    b2Filter filter;
    filter.categoryBits = category;
    filter.maskBits = mask;
    filter.groupIndex = 0;
    return filter;
}

// 减少不必要的碰撞检测
void SetupCollisionFilters() {
    // 投射物不与道具碰撞
    b2Filter projectileFilter = CreateOptimizedFilter(
        LAYER_PROJECTILE,
        LAYER_STATIC | LAYER_PLAYER | LAYER_ENEMY
    );

    // 特效只作为传感器，不产生物理响应
    b2Filter effectFilter = CreateOptimizedFilter(
        LAYER_EFFECT,
        LAYER_PLAYER | LAYER_ENEMY
    );
}
```

#### 睡眠优化
```cpp
// 主动管理物体睡眠
void OptimizeSleeping(b2BodyId bodyId) {
    b2Vec2 velocity = b2Body_GetLinearVelocity(bodyId);
    float angularVel = b2Body_GetAngularVelocity(bodyId);

    // 低速物体强制睡眠
    if (b2Length(velocity) < 0.1f && fabsf(angularVel) < 0.1f) {
        b2Body_SetAwake(bodyId, false);
    }
}

// 区域性唤醒
void WakeNearbyBodies(b2WorldId worldId, b2Vec2 center, float radius) {
    b2AABB queryAABB;
    queryAABB.lowerBound = {center.x - radius, center.y - radius};
    queryAABB.upperBound = {center.x + radius, center.y + radius};

    // 查询回调中唤醒物体
    // (需要实现查询回调函数)
}
```

### 10.4 调试工具

#### 性能监控
```cpp
typedef struct PerformanceMonitor {
    float stepTime;
    float collisionTime;
    float solverTime;
    int bodyCount;
    int contactCount;
    int jointCount;
} PerformanceMonitor;

void UpdatePerformanceMonitor(b2WorldId worldId, PerformanceMonitor* monitor) {
    b2Counters counters = b2World_GetCounters(worldId);
    b2Profile profile = b2World_GetProfile(worldId);

    monitor->stepTime = profile.step;
    monitor->collisionTime = profile.collide;
    monitor->solverTime = profile.solve;
    monitor->bodyCount = counters.bodyCount;
    monitor->contactCount = counters.contactCount;
    monitor->jointCount = counters.jointCount;
}

void PrintPerformanceStats(PerformanceMonitor* monitor) {
    printf("=== Performance Stats ===\n");
    printf("Step time: %.3f ms\n", monitor->stepTime * 1000.0f);
    printf("Collision time: %.3f ms\n", monitor->collisionTime * 1000.0f);
    printf("Solver time: %.3f ms\n", monitor->solverTime * 1000.0f);
    printf("Bodies: %d\n", monitor->bodyCount);
    printf("Contacts: %d\n", monitor->contactCount);
    printf("Joints: %d\n", monitor->jointCount);
}
```

---

## 11. 常见问题与解决方案

### 11.1 物体穿透

#### 问题描述
高速移动的物体穿过薄墙或其他物体。

#### 解决方案
```cpp
// 1. 启用连续碰撞检测 (CCD)
b2BodyDef bodyDef = b2DefaultBodyDef();
bodyDef.type = b2_dynamicBody;
bodyDef.isBullet = true;  // 启用CCD

// 2. 增加墙体厚度
b2Polygon thickWall = b2MakeBox(0.5f, 5.0f);  // 厚度0.5米

// 3. 限制物体速度
void LimitBodySpeed(b2BodyId bodyId, float maxSpeed) {
    b2Vec2 velocity = b2Body_GetLinearVelocity(bodyId);
    float speed = b2Length(velocity);
    if (speed > maxSpeed) {
        velocity = b2MulSV(maxSpeed / speed, velocity);
        b2Body_SetLinearVelocity(bodyId, velocity);
    }
}

// 4. 使用更小的时间步长
const float timeStep = 1.0f / 120.0f;  // 120 FPS instead of 60
```

### 11.2 不稳定的关节

#### 问题描述
关节连接不稳定，物体抖动或分离。

#### 解决方案
```cpp
// 1. 调整关节参数
b2RevoluteJointDef jointDef = b2DefaultRevoluteJointDef();
// 增加约束强度
jointDef.base.collideConnected = false;  // 禁用连接物体间碰撞

// 2. 使用焊接关节替代旋转关节 (如果不需要旋转)
b2WeldJointDef weldDef = b2DefaultWeldJointDef();
weldDef.linearHertz = 8.0f;      // 增加刚度
weldDef.linearDampingRatio = 1.0f;  // 增加阻尼

// 3. 减少连接物体的质量差异
void BalanceJointMasses(b2BodyId bodyA, b2BodyId bodyB) {
    float massA = b2Body_GetMass(bodyA);
    float massB = b2Body_GetMass(bodyB);

    if (massA / massB > 10.0f || massB / massA > 10.0f) {
        printf("Warning: Large mass ratio in joint connection\n");
    }
}
```

### 11.3 性能问题

#### 问题描述
帧率下降，物理模拟卡顿。

#### 解决方案
```cpp
// 1. 减少物体数量
void CleanupDistantBodies(b2WorldId worldId, b2Vec2 playerPos, float maxDistance) {
    // 实现：销毁距离玩家太远的物体
}

// 2. 优化碰撞检测
void OptimizeCollisionDetection() {
    // 使用更少的碰撞层
    // 禁用不必要的碰撞
    // 使用传感器替代物理碰撞
}

// 3. 调整模拟参数
const float timeStep = 1.0f / 60.0f;
const int subStepCount = 2;  // 减少子步数
b2World_Step(worldId, timeStep, subStepCount);
```

### 11.4 精度问题

#### 问题描述
物体位置不准确，累积误差。

#### 解决方案
```cpp
// 1. 使用合适的单位尺度
const float PIXELS_PER_METER = 100.0f;  // 1米 = 100像素

// 2. 定期重置物体位置
void CorrectBodyPosition(b2BodyId bodyId, b2Vec2 targetPos) {
    b2Vec2 currentPos = b2Body_GetPosition(bodyId);
    b2Vec2 error = b2Sub(targetPos, currentPos);

    if (b2Length(error) > 0.1f) {  // 误差超过0.1米
        b2Body_SetTransform(bodyId, targetPos, b2Body_GetRotation(bodyId));
    }
}

// 3. 避免极小或极大的数值
bool IsValidPhysicsValue(float value) {
    return !isnan(value) && !isinf(value) &&
           fabsf(value) < 1e6f && fabsf(value) > 1e-6f;
}
```

---

## 9. 实际应用示例

### 9.1 基础物理世界

#### 完整的物理世界创建流程
```cpp
#include "box2d/box2d.h"
#include <stdio.h>

int main() {
    // 1. 创建世界
    b2WorldDef worldDef = b2DefaultWorldDef();
    worldDef.gravity = {0.0f, -9.8f};  // 向下的重力
    b2WorldId worldId = b2CreateWorld(&worldDef);

    // 2. 创建地面 (静态物体)
    b2BodyDef groundDef = b2DefaultBodyDef();
    groundDef.position = {0.0f, -10.0f};
    b2BodyId groundId = b2CreateBody(worldId, &groundDef);

    b2Polygon groundBox = b2MakeBox(50.0f, 10.0f);
    b2ShapeDef groundShapeDef = b2DefaultShapeDef();
    groundShapeDef.friction = 0.6f;
    b2CreatePolygonShape(groundId, &groundShapeDef, &groundBox);

    // 3. 创建动态物体
    b2BodyDef bodyDef = b2DefaultBodyDef();
    bodyDef.type = b2_dynamicBody;
    bodyDef.position = {0.0f, 4.0f};
    b2BodyId bodyId = b2CreateBody(worldId, &bodyDef);

    b2Polygon dynamicBox = b2MakeBox(1.0f, 1.0f);
    b2ShapeDef shapeDef = b2DefaultShapeDef();
    shapeDef.density = 1.0f;
    shapeDef.friction = 0.3f;
    shapeDef.restitution = 0.5f;  // 弹性
    b2CreatePolygonShape(bodyId, &shapeDef, &dynamicBox);

    // 4. 物理模拟循环
    float timeStep = 1.0f / 60.0f;
    for (int i = 0; i < 300; ++i) {  // 5秒模拟
        b2World_Step(worldId, timeStep, 4);

        // 每30帧输出一次位置
        if (i % 30 == 0) {
            b2Vec2 position = b2Body_GetPosition(bodyId);
            float angle = b2Rot_GetAngle(b2Body_GetRotation(bodyId));

            printf("Frame %d: Position (%.2f, %.2f), Angle %.2f°\n",
                   i, position.x, position.y, angle * 180.0f / M_PI);
        }
    }

    // 5. 清理资源
    b2DestroyWorld(worldId);
    return 0;
}
```

### 9.2 平台游戏

#### 角色控制器
```cpp
typedef struct PlatformPlayer {
    b2BodyId bodyId;
    b2ShapeId shapeId;
    bool onGround;
    float moveSpeed;
    float jumpForce;
} PlatformPlayer;

PlatformPlayer* CreatePlayer(b2WorldId worldId, b2Vec2 position) {
    PlatformPlayer* player = malloc(sizeof(PlatformPlayer));

    // 创建角色物体
    b2BodyDef bodyDef = b2DefaultBodyDef();
    bodyDef.type = b2_dynamicBody;
    bodyDef.position = position;
    bodyDef.fixedRotation = true;  // 防止旋转
    player->bodyId = b2CreateBody(worldId, &bodyDef);

    // 创建胶囊形状 (避免卡边)
    b2Capsule capsule;
    capsule.center1 = {0.0f, -0.8f};
    capsule.center2 = {0.0f, 0.8f};
    capsule.radius = 0.4f;

    b2ShapeDef shapeDef = b2DefaultShapeDef();
    shapeDef.density = 1.0f;
    shapeDef.friction = 0.0f;  // 无摩擦，避免粘墙
    player->shapeId = b2CreateCapsuleShape(player->bodyId, &shapeDef, &capsule);

    player->onGround = false;
    player->moveSpeed = 8.0f;
    player->jumpForce = 400.0f;

    return player;
}

void UpdatePlayer(PlatformPlayer* player, bool moveLeft, bool moveRight, bool jump) {
    b2Vec2 velocity = b2Body_GetLinearVelocity(player->bodyId);

    // 水平移动
    float targetVelX = 0.0f;
    if (moveLeft) targetVelX = -player->moveSpeed;
    if (moveRight) targetVelX = player->moveSpeed;

    // 设置水平速度，保持垂直速度
    velocity.x = targetVelX;
    b2Body_SetLinearVelocity(player->bodyId, velocity);

    // 跳跃
    if (jump && player->onGround) {
        b2Vec2 jumpImpulse = {0.0f, -player->jumpForce};
        b2Body_ApplyLinearImpulseToCenter(player->bodyId, jumpImpulse, true);
        player->onGround = false;
    }
}

// 地面检测 (使用射线投射)
void CheckGroundContact(PlatformPlayer* player, b2WorldId worldId) {
    b2Vec2 position = b2Body_GetPosition(player->bodyId);

    b2RaycastInput rayInput;
    rayInput.origin = position;
    rayInput.translation = {0.0f, 1.0f};  // 向下1米
    rayInput.maxFraction = 1.0f;

    b2RaycastResult result = b2World_CastRay(worldId, &rayInput);
    player->onGround = result.hit && result.fraction < 0.6f;
}
```

### 9.3 车辆模拟

#### 简单车辆系统
```cpp
typedef struct SimpleVehicle {
    b2BodyId chassis;
    b2BodyId frontWheel;
    b2BodyId rearWheel;
    b2JointId frontJoint;
    b2JointId rearJoint;
    float motorSpeed;
    float maxMotorTorque;
} SimpleVehicle;

SimpleVehicle* CreateVehicle(b2WorldId worldId, b2Vec2 position) {
    SimpleVehicle* vehicle = malloc(sizeof(SimpleVehicle));

    // 创建车身
    b2BodyDef chassisDef = b2DefaultBodyDef();
    chassisDef.type = b2_dynamicBody;
    chassisDef.position = position;
    vehicle->chassis = b2CreateBody(worldId, &chassisDef);

    b2Polygon chassisShape = b2MakeBox(2.0f, 0.5f);
    b2ShapeDef chassisShapeDef = b2DefaultShapeDef();
    chassisShapeDef.density = 1.0f;
    b2CreatePolygonShape(vehicle->chassis, &chassisShapeDef, &chassisShape);

    // 创建前轮
    b2BodyDef wheelDef = b2DefaultBodyDef();
    wheelDef.type = b2_dynamicBody;
    wheelDef.position = {position.x + 1.5f, position.y - 1.0f};
    vehicle->frontWheel = b2CreateBody(worldId, &wheelDef);

    b2Circle wheelShape = {{0,0}, 0.5f};
    b2ShapeDef wheelShapeDef = b2DefaultShapeDef();
    wheelShapeDef.density = 1.0f;
    wheelShapeDef.friction = 0.9f;  // 高摩擦
    b2CreateCircleShape(vehicle->frontWheel, &wheelShapeDef, &wheelShape);

    // 创建后轮
    wheelDef.position = {position.x - 1.5f, position.y - 1.0f};
    vehicle->rearWheel = b2CreateBody(worldId, &wheelDef);
    b2CreateCircleShape(vehicle->rearWheel, &wheelShapeDef, &wheelShape);

    // 创建轮子关节
    b2WheelJointDef frontJointDef = b2DefaultWheelJointDef();
    frontJointDef.base.bodyIdA = vehicle->chassis;
    frontJointDef.base.bodyIdB = vehicle->frontWheel;
    frontJointDef.base.localFrameA.p = {1.5f, -1.0f};
    frontJointDef.base.localFrameB.p = {0.0f, 0.0f};
    frontJointDef.localAxisA = {0.0f, 1.0f};  // 垂直悬挂
    frontJointDef.enableSpring = true;
    frontJointDef.hertz = 4.0f;
    frontJointDef.dampingRatio = 0.7f;
    frontJointDef.enableMotor = true;
    frontJointDef.maxMotorTorque = 100.0f;
    vehicle->frontJoint = b2CreateWheelJoint(worldId, &frontJointDef);

    // 后轮关节 (类似前轮)
    b2WheelJointDef rearJointDef = frontJointDef;
    rearJointDef.base.bodyIdB = vehicle->rearWheel;
    rearJointDef.base.localFrameA.p = {-1.5f, -1.0f};
    vehicle->rearJoint = b2CreateWheelJoint(worldId, &rearJointDef);

    vehicle->motorSpeed = 0.0f;
    vehicle->maxMotorTorque = 100.0f;

    return vehicle;
}

void UpdateVehicle(SimpleVehicle* vehicle, float throttle, float brake) {
    // 计算马达速度
    vehicle->motorSpeed = throttle * 20.0f;  // 最大20弧度/秒

    // 设置轮子马达
    b2WheelJoint_SetMotorSpeed(vehicle->frontJoint, vehicle->motorSpeed);
    b2WheelJoint_SetMotorSpeed(vehicle->rearJoint, vehicle->motorSpeed);

    // 刹车 (增加阻尼)
    if (brake > 0.0f) {
        b2Vec2 velocity = b2Body_GetLinearVelocity(vehicle->chassis);
        b2Vec2 brakeForce = b2MulSV(-brake * 50.0f, velocity);
        b2Body_ApplyForceToCenter(vehicle->chassis, brakeForce, true);
    }
}
```

### 9.4 机械装置

#### 简单机械臂
```cpp
typedef struct RoboticArm {
    b2BodyId base;
    b2BodyId upperArm;
    b2BodyId lowerArm;
    b2BodyId endEffector;
    b2JointId shoulderJoint;
    b2JointId elbowJoint;
    b2JointId wristJoint;
} RoboticArm;

RoboticArm* CreateRoboticArm(b2WorldId worldId, b2Vec2 basePosition) {
    RoboticArm* arm = malloc(sizeof(RoboticArm));

    // 创建基座
    b2BodyDef baseDef = b2DefaultBodyDef();
    baseDef.position = basePosition;
    arm->base = b2CreateBody(worldId, &baseDef);

    b2Polygon baseShape = b2MakeBox(1.0f, 0.5f);
    b2ShapeDef baseShapeDef = b2DefaultShapeDef();
    baseShapeDef.density = 2.0f;
    b2CreatePolygonShape(arm->base, &baseShapeDef, &baseShape);

    // 创建上臂
    b2BodyDef upperArmDef = b2DefaultBodyDef();
    upperArmDef.type = b2_dynamicBody;
    upperArmDef.position = {basePosition.x, basePosition.y + 1.5f};
    arm->upperArm = b2CreateBody(worldId, &upperArmDef);

    b2Polygon upperArmShape = b2MakeBox(0.2f, 1.0f);
    b2ShapeDef armShapeDef = b2DefaultShapeDef();
    armShapeDef.density = 1.0f;
    b2CreatePolygonShape(arm->upperArm, &armShapeDef, &upperArmShape);

    // 创建下臂
    b2BodyDef lowerArmDef = b2DefaultBodyDef();
    lowerArmDef.type = b2_dynamicBody;
    lowerArmDef.position = {basePosition.x, basePosition.y + 3.5f};
    arm->lowerArm = b2CreateBody(worldId, &lowerArmDef);
    b2CreatePolygonShape(arm->lowerArm, &armShapeDef, &upperArmShape);

    // 创建末端执行器
    b2BodyDef endDef = b2DefaultBodyDef();
    endDef.type = b2_dynamicBody;
    endDef.position = {basePosition.x, basePosition.y + 4.8f};
    arm->endEffector = b2CreateBody(worldId, &endDef);

    b2Polygon endShape = b2MakeBox(0.3f, 0.3f);
    b2CreatePolygonShape(arm->endEffector, &armShapeDef, &endShape);

    // 创建关节
    // 肩关节
    b2RevoluteJointDef shoulderDef = b2DefaultRevoluteJointDef();
    shoulderDef.base.bodyIdA = arm->base;
    shoulderDef.base.bodyIdB = arm->upperArm;
    shoulderDef.base.localFrameA.p = {0.0f, 0.5f};
    shoulderDef.base.localFrameB.p = {0.0f, -1.0f};
    shoulderDef.enableLimit = true;
    shoulderDef.lowerAngle = -M_PI / 2;
    shoulderDef.upperAngle = M_PI / 2;
    shoulderDef.enableMotor = true;
    shoulderDef.maxMotorTorque = 50.0f;
    arm->shoulderJoint = b2CreateRevoluteJoint(worldId, &shoulderDef);

    // 肘关节
    b2RevoluteJointDef elbowDef = shoulderDef;
    elbowDef.base.bodyIdA = arm->upperArm;
    elbowDef.base.bodyIdB = arm->lowerArm;
    elbowDef.base.localFrameA.p = {0.0f, 1.0f};
    elbowDef.base.localFrameB.p = {0.0f, -1.0f};
    elbowDef.lowerAngle = -M_PI;
    elbowDef.upperAngle = 0.0f;
    arm->elbowJoint = b2CreateRevoluteJoint(worldId, &elbowDef);

    // 腕关节
    b2RevoluteJointDef wristDef = shoulderDef;
    wristDef.base.bodyIdA = arm->lowerArm;
    wristDef.base.bodyIdB = arm->endEffector;
    wristDef.base.localFrameA.p = {0.0f, 1.0f};
    wristDef.base.localFrameB.p = {0.0f, 0.0f};
    wristDef.lowerAngle = -M_PI;
    wristDef.upperAngle = M_PI;
    arm->wristJoint = b2CreateRevoluteJoint(worldId, &wristDef);

    return arm;
}

void ControlRoboticArm(RoboticArm* arm, float shoulderSpeed, float elbowSpeed, float wristSpeed) {
    b2RevoluteJoint_SetMotorSpeed(arm->shoulderJoint, shoulderSpeed);
    b2RevoluteJoint_SetMotorSpeed(arm->elbowJoint, elbowSpeed);
    b2RevoluteJoint_SetMotorSpeed(arm->wristJoint, wristSpeed);
}
```

这个详细的Box2D 3.0使用指南现在包含了：

1. **基础概念** - 坐标系统、单位系统、时间步长
2. **World管理** - 创建、配置、模拟、事件处理
3. **Body系统** - 类型、创建、变换、速度、力、质量、状态
4. **Shape系统** - 圆形、多边形、胶囊、线段、属性、过滤、传感器
5. **Joint系统** - 旋转、平移、距离、焊接、轮子、马达关节
6. **碰撞检测** - 事件、射线投射、形状查询、AABB查询
7. **ID系统** - 类型安全、验证、生命周期管理
8. **数学工具** - 向量、旋转、变换、几何计算
9. **实际应用** - 基础世界、平台游戏、车辆、机械装置
10. **性能优化** - 内存管理、计算优化、调试工具
11. **常见问题** - 穿透、不稳定、性能、精度问题

这是一个完整的Box2D 3.0参考手册，涵盖了从基础概念到高级应用的所有内容！

### 创建旋转关节连接
```cpp
// 创建两个物体
b2BodyId bodyA = CreateDynamicBody(worldId, {-2.0f, 0.0f});
b2BodyId bodyB = CreateDynamicBody(worldId, {2.0f, 0.0f});

// 创建旋转关节
b2RevoluteJointDef jointDef = b2DefaultRevoluteJointDef();
jointDef.base.bodyIdA = bodyA;
jointDef.base.bodyIdB = bodyB;
jointDef.base.localFrameA.p = {1.0f, 0.0f};  // bodyA上的连接点
jointDef.base.localFrameB.p = {-1.0f, 0.0f}; // bodyB上的连接点

b2JointId jointId = b2CreateRevoluteJoint(worldId, &jointDef);

// 启用马达
b2RevoluteJoint_EnableMotor(jointId, true);
b2RevoluteJoint_SetMotorSpeed(jointId, 1.0f);  // 1弧度/秒
b2RevoluteJoint_SetMaxMotorTorque(jointId, 10.0f);
```

### 力和运动控制
```cpp
// 施加持续的力 (如推力)
b2Vec2 force = {100.0f, 0.0f};
b2Body_ApplyForceToCenter(bodyId, force, true);

// 施加瞬时冲量 (如跳跃)
b2Vec2 impulse = {0.0f, -50.0f};
b2Body_ApplyLinearImpulseToCenter(bodyId, impulse, true);

// 直接设置速度 (如平台游戏移动)
b2Vec2 velocity = {5.0f, 0.0f};
b2Body_SetLinearVelocity(bodyId, velocity);

// 施加扭矩 (旋转)
float torque = 10.0f;
b2Body_ApplyTorque(bodyId, torque, true);
```

### 碰撞检测和传感器
```cpp
// 创建传感器
b2ShapeDef sensorDef = b2DefaultShapeDef();
sensorDef.isSensor = true;  // 设置为传感器
b2Circle sensorCircle;
sensorCircle.center = {0.0f, 0.0f};
sensorCircle.radius = 2.0f;
b2ShapeId sensorId = b2CreateCircleShape(bodyId, &sensorDef, &sensorCircle);

// 点测试
b2Vec2 testPoint = {1.0f, 1.0f};
bool isInside = b2Shape_TestPoint(sensorId, testPoint);
```

### 坐标转换
```cpp
// 世界坐标转换为本地坐标
b2Vec2 worldPoint = {5.0f, 3.0f};
b2Vec2 localPoint = b2Body_GetLocalPoint(bodyId, worldPoint);

// 本地坐标转换为世界坐标
b2Vec2 localPoint = {1.0f, 0.0f};
b2Vec2 worldPoint = b2Body_GetWorldPoint(bodyId, localPoint);

// 世界向量转换为本地向量
b2Vec2 worldVector = {1.0f, 1.0f};
b2Vec2 localVector = b2Body_GetLocalVector(bodyId, worldVector);
```

### 最佳实践

#### 1. 内存管理
- 始终检查ID是否有效: `B2_IS_NON_NULL(id)`
- 及时销毁不需要的对象
- 避免使用已销毁对象的ID

#### 2. 性能优化
- 合理设置物体的睡眠状态
- 使用适当的时间步长 (通常1/60秒)
- 避免过多的小物体

#### 3. 物理参数调优
- 密度: 影响质量和惯性
- 摩擦力: 0.0-1.0，影响滑动
- 恢复系数: 0.0-1.0，影响弹性
- 阻尼: 减少不必要的振动

#### 4. 关节使用
- 选择合适的关节类型
- 合理设置关节限制
- 注意关节的稳定性

---

## 常用数学函数

### 向量操作
```cpp
// 向量长度
float b2Length(b2Vec2 v);

// 向量归一化
b2Vec2 b2Normalize(b2Vec2 v);

// 向量点积
float b2Dot(b2Vec2 a, b2Vec2 b);

// 向量叉积
float b2Cross(b2Vec2 a, b2Vec2 b);
```

### 旋转操作
```cpp
// 创建旋转
b2Rot b2MakeRot(float angle);

// 获取角度
float b2Rot_GetAngle(b2Rot q);

// 旋转向量
b2Vec2 b2RotateVector(b2Rot q, b2Vec2 v);
```

---

这个指南涵盖了Box2D 3.0的核心功能和使用方法。在实际开发中，建议从简单的示例开始，逐步掌握各种功能的使用。
