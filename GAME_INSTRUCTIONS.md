# 羽毛球游戏使用说明

## 游戏启动
1. 运行 `bdmt.exe`
2. 在主页面按 **空格键** 开始游戏

## 游戏控制

### 玩家1 (绿色，左侧)
- **A键**: 向左移动
- **D键**: 向右移动  
- **W键**: 向上挥拍 (从下往上挥拍)
- **S键**: 向下挥拍 (从上往下挥拍)
- **Q键**: 跳跃

### 玩家2 (蓝色，右侧)
- **左方向键**: 向左移动
- **右方向键**: 向右移动
- **上方向键**: 向上挥拍 (从下往上挥拍)
- **下方向键**: 向下挥拍 (从上往下挥拍)
- **右Shift键**: 跳跃

## 游戏目标
- 双人对打羽毛球
- 使用球拍击打羽毛球
- 尝试让球落在对方场地

## 调试信息
- 游戏运行时会在 `debug_log.txt` 文件中输出调试信息
- 调试信息包括玩家移动、挥拍、跳跃等操作的详细日志
- 调试语句用 `//***********` 标记，方便识别

## 注意事项
- 确保系统中有 arial.ttf 或 calibri.ttf 字体文件
- 游戏使用Box2D物理引擎，所有动作都有物理反馈
- 只有在地面上才能跳跃
- 球拍挥拍有动画效果，从指定角度挥到水平位置

## 故障排除
如果游戏无法正常运行：
1. 检查 `debug_log.txt` 文件中的错误信息
2. 确保所有依赖库正确安装
3. 检查字体文件是否存在
