#include <sfml/Graphics.hpp>
#include "Player.hpp"
#include "PhysicsWorld.hpp"
#include "GameConfig.hpp"
#include "Shuttlecock.hpp"
#include <set>


class GameState
{
    enum State
    {
        Gaming,
        Paused,
        GameOver,
    };
int player1_score;
int player2_score;
State state;
};

class GameScene
{
public:
    GameScene(int width = 800, int height = 600, const char *title = "Badminton Game");
    void processGamingInput(sf::Event &event); // 处理游戏中的输入
    void renderGame();                         // 渲染游戏画面
    void controlPlayer();                      // 控制玩家
    ~GameScene();

    void Game();                               // 具体的游戏逻辑


private:
    sf::RenderWindow *window;                  // 游戏窗口
    std::set<sf::Keyboard::Key> pressedKeys;   // 记录按下的键
    sf::Clock clock;                           // 时钟，用来记录deltaTime，以此准确的推动物理时间

    GameState gameState;                       // 游戏信息


    // 物理世界
    PhysicsWorld *physicsWorld;

    // 玩家对象
    Player *player1;
    Player *player2;

    // 羽毛球
    Shuttlecock *shuttlecock;

    // 窗口尺寸
    int windowWidth;
    int windowHeight;
};