#include "../include/Player.hpp"
#include "../include/Racket.hpp"
#include "../include/PhysicsWorld.hpp"
#include <SFML/Graphics.hpp>
#include <cmath>
#include <fstream>

Player::Player(int playerId, sf::Color color, b2WorldId worldId)
    : physicsWorldId(worldId), racket(nullptr), physicsBody(b2_nullBodyId), playerId(playerId)
{
    // 初始化渲染形状
    playerShape.setRadius(GameConfig::getPlayerRadius());
    // 设置原点为中心
    playerShape.setOrigin(GameConfig::getPlayerRadius(), GameConfig::getPlayerRadius());
    playerShape.setFillColor(color);
    legShape.setSize(sf::Vector2f(GameConfig::getPlayerRadius() * 1.5f, GameConfig::getPlayerRadius() * 0.5f));
    legShape.setOrigin(GameConfig::getPlayerRadius() * 0.75f, GameConfig::getPlayerRadius() * 0.25f);
    legShape.setFillColor(color);
    if (playerId == 1)
        position = GameConfig::getPlayer1StartPos();
    else
        position = GameConfig::getPlayer2StartPos();
    // 创建物理体
    createPhysicsBody();

    speed = GameConfig::PLAYER_SPEED / PhysicsWorld::PIXELS_PER_METER;
}

void Player::draw(sf::RenderWindow &window)
{
    updateFromPhysics();
    window.draw(playerShape);
    window.draw(legShape);

    // 渲染球拍
    if (racket)
    {
        racket->draw(window);
    }
}

sf::Vector2f Player::getPosition() const
{
    return position;
}
void Player::setGraphicPosition(float x, float y)
{
    playerShape.setPosition(x, y);
    legShape.setPosition(x, y + GameConfig::getPlayerRadius());
}
void Player::setPhysicsPostion(float x, float y)
{
    if (B2_IS_NON_NULL(physicsBody))
    {
        b2Body_SetTransform(physicsBody, PhysicsWorld::sfToB2(sf::Vector2f(x, y)), b2MakeRot(0));
    }
}
void Player::setPosition(float x, float y)
{
    position = sf::Vector2f(x, y);
    setPhysicsPostion(x, y);
    setGraphicPosition(x, y);
}

void Player::move(Direction direction)
{
    //TODO
    // 使用物理引擎来移动，移动逻辑见说明
    if (B2_IS_NULL(physicsBody))
    {
        return;
    }

    if(direction.y()==1.0f){
        jump(GameConfig::JUMP_FORCE);
    }
    // 使用物理引擎的力来移动
    b2Vec2 currentVelocity = b2Body_GetLinearVelocity(physicsBody);

    // 只在水平方向应用力，保持垂直速度不变
    float targetVelocityX = direction.x() * speed;
    float velocityChangeX = targetVelocityX - currentVelocity.x;

    // 应用水平方向的冲量
    if (std::abs(velocityChangeX) > 0.1f)
    {
        b2Vec2 impulse = {velocityChangeX * b2Body_GetMass(physicsBody), 0.0f};
        b2Body_ApplyLinearImpulseToCenter(physicsBody, impulse, true);
    }
}
bool Player::isOnGround() const
{
    if (B2_IS_NULL(physicsBody))
        return false;

    b2Vec2 velocity = b2Body_GetLinearVelocity(physicsBody);
    b2Vec2 pos = b2Body_GetPosition(physicsBody);

    // 检查是否接近地面且垂直速度很小
    float groundLevel = GameConfig::getGroundY() / GameConfig::PIXELS_PER_METER;
    float playerBottom = pos.y + GameConfig::getPlayerRadius() / GameConfig::PIXELS_PER_METER;

    return (playerBottom >= groundLevel - 0.1f) && (velocity.y >= -0.1f);
}

void Player::createPhysicsBody()
{
    if (B2_IS_NULL(physicsWorldId))
        return;

    b2BodyDef bodyDef = b2DefaultBodyDef();
    bodyDef.type = b2_dynamicBody;
    bodyDef.position = PhysicsWorld::sfToB2(position);

    physicsBody = b2CreateBody(physicsWorldId, &bodyDef);

    // 创建圆形碰撞体
    b2Circle circle;
    circle.center = {0.0f, 0.0f};
    circle.radius = GameConfig::getPlayerRadius() / GameConfig::PIXELS_PER_METER;

    b2ShapeDef shapeDef = b2DefaultShapeDef();
    shapeDef.density = 1.0f;

    b2ShapeId shapeId = b2CreateCircleShape(physicsBody, &shapeDef, &circle);

    // 设置完全非弹性碰撞，人物落地不会跳起
    b2Shape_SetRestitution(shapeId, 0.0f);

    // 创建球拍
    racket = new Racket(physicsWorldId, position, GameConfig::racketColor);
    // 将球杆用旋转关节连接到玩家上
    racket->createJoint(physicsWorldId, physicsBody, {GameConfig::getPlayerRadius() / GameConfig::PIXELS_PER_METER, 0.0f});

    setGraphicPosition(position.x, position.y);
}

void Player::updateFromPhysics()
{
    if (B2_IS_NON_NULL(physicsBody))
    {
        b2Vec2 physicsPos = b2Body_GetPosition(physicsBody);
        sf::Vector2f newPos = PhysicsWorld::b2ToSf(physicsPos);
        position = newPos; // 更新position变量
        setGraphicPosition(newPos.x, newPos.y);

        // 更新球拍位置跟随玩家
        if (racket)
        {
            racket->updatePosition(position);
            racket->update(); // 更新球拍状态（自动回位等）
        }
    }
}

void Player::swing(Direction direction)
{
    // TODO
    // 细化挥拍逻辑，具体见说明
    if (racket)
    {
        // 根据方向决定挥拍方向
        // direction.y() < 0 表示向上挥拍 (从下往上)
        // direction.y() > 0 表示向下挥拍 (从上往下)
        float swingDirection = direction.y() > 0 ? 1.0f : -1.0f;
        racket->rotate(swingDirection);

        // 挥拍后短暂延迟重置（这里简化为立即重置）
        // 在实际游戏中可以添加计时器
        // racket->resetAngle();
    }
}

void Player::jump(float jumpForce)
{
    if (B2_IS_NULL(physicsBody))
    {
        return;
    }

    // 只有在地面上才能跳跃
    if (isOnGround())
    {
        // 应用向上的冲量
        b2Vec2 jumpImpulse = {0.0f, -jumpForce / GameConfig::PIXELS_PER_METER};
        b2Body_ApplyLinearImpulseToCenter(physicsBody, jumpImpulse, true);
    }
}


Player::~Player()
{
    if (racket)
    {
        delete racket;
    }
    if (B2_IS_NON_NULL(physicsBody) && B2_IS_NON_NULL(physicsWorldId))
    {
        b2DestroyBody(physicsBody);
    }
}