#pragma once
#include <SFML/Graphics.hpp>
#include <box2d/box2d.h>
#include "PhysicsWorld.hpp"
#include "GameConfig.hpp"

class Racket
{
public:
    Racket(b2WorldId worldId, sf::Vector2f playerPosition, sf::Color color = sf::Color::Yellow);
    ~Racket();
    // 返回球拍物理体
    b2BodyId getHandleBody() { return handleBody; }
    void updateState();
    void updatePosition(sf::Vector2f newPlayerPosition); // 更新球拍位置跟随玩家
    void update();                                       // 更新球拍状态（自动回位等）
    void draw(sf::RenderWindow &window);
    void rotate(float direction);
    void resetAngle();                                // 重置球拍角度到初始位置
    void setSwinging(int state) { swinging = state; } // 设置挥拍状态
    void createJoint(b2WorldId worldId, b2BodyId playerBody, b2Vec2 playerAnchor);
    void setAngle(float TargetAngle);

private:
    b2WorldId worldId;
    b2BodyId handleBody;
    b2BodyId headBody;

    // 渲染用的SFML图形
    sf::RectangleShape handleShape; // 拍柄
    sf::RectangleShape headShape;   // 拍头

    float angle;           // 球拍角度
    float angularVelocity; // 角速度
    int swinging;          // 0: 未挥拍 1: 逆时针挥拍中 2: 顺时针挥拍中

    sf::Vector2f playerPosition; // 玩家位置
    sf::Color racketColor;
    b2JointId jointId;

    void createPhysicsBody();
    void setRenderShapes();
};
