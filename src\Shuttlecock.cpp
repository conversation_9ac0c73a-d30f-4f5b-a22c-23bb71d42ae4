#include "../include/Shuttlecock.hpp"
#include <cmath>
#include <fstream>

Shuttlecock::Shuttlecock(b2WorldId worldId, sf::Vector2f startPosition)
    : worldId(worldId), physicsBody(b2_nullBodyId), position(startPosition)
{
    // 设置渲染形状
    float radius = GameConfig::getBallRadius();
    ballShape.setRadius(radius);
    ballShape.setOrigin(radius, radius);
    ballShape.setFillColor(sf::Color::White);
    ballShape.setOutlineThickness(1.0f);
    ballShape.setOutlineColor(sf::Color::Black);

    // 创建物理体
    if (B2_IS_NON_NULL(worldId))
    {
        createPhysicsBody();
    }

    // 设置初始位置
    reset(startPosition);
}

Shuttlecock::~Shuttlecock()
{
    if (B2_IS_NON_NULL(physicsBody) && B2_IS_NON_NULL(worldId))
    {
        b2DestroyBody(physicsBody);
    }
}

void Shuttlecock::update(float deltaTime)
{
    if (B2_IS_NON_NULL(physicsBody))
    {
        // 完全弹性碰撞，不应用空气阻力
        // applyAirResistance(); // 注释掉空气阻力

        // 从物理体更新渲染位置
        updateFromPhysics();
    }
}

void Shuttlecock::render(sf::RenderWindow &window)
{
    // 首先从物理体更新位置
    updateFromPhysics();

    // 然后渲染
    window.draw(ballShape);
}

void Shuttlecock::reset(sf::Vector2f newPosition)
{
    position = newPosition;
    ballShape.setPosition(position);

    if (B2_IS_NON_NULL(physicsBody))
    {
        // 重置物理体位置和速度
        b2Body_SetTransform(physicsBody, PhysicsWorld::sfToB2(position), b2MakeRot(0));
        b2Body_SetLinearVelocity(physicsBody, {0.0f, 0.0f});
        b2Body_SetAngularVelocity(physicsBody, 0.0f);
    }
}

sf::Vector2f Shuttlecock::getPosition() const
{
    return position;
}

bool Shuttlecock::isOutOfBounds(int windowWidth, int windowHeight) const
{
    float radius = GameConfig::getBallRadius();
    return (position.x < -radius ||
            position.x > windowWidth + radius ||
            position.y < -radius ||
            position.y > windowHeight + radius);
}

bool Shuttlecock::hasHitGround(int windowHeight) const
{
    float radius = GameConfig::getBallRadius();
    float groundY = GameConfig::getGroundY();
    return (position.y + radius >= groundY);
}

sf::Vector2f Shuttlecock::getResetPosition(int windowWidth, int windowHeight) const
{
    // 根据球的位置决定在哪一侧重新开始
    float centerX = windowWidth / 2.0f;
    float resetX;

    if (position.x < centerX)
    {
        // 球在左侧，在左侧1/4处重新开始
        resetX = windowWidth * 0.25f;
    }
    else
    {
        // 球在右侧，在右侧3/4处重新开始
        resetX = windowWidth * 0.75f;
    }

    // 在较高的位置重新开始
    float resetY = windowHeight * 0.3f;

    return sf::Vector2f(resetX, resetY);
}

void Shuttlecock::createPhysicsBody()
{
    b2BodyDef bodyDef = b2DefaultBodyDef();
    bodyDef.type = b2_dynamicBody;
    bodyDef.position = PhysicsWorld::sfToB2(position);

    physicsBody = b2CreateBody(worldId, &bodyDef);

    //***********
    std::ofstream debugLog("physics_debug.txt", std::ios::app);
    debugLog << "Shuttlecock physics body created as DYNAMIC at position: ("
             << bodyDef.position.x << ", " << bodyDef.position.y << ")" << std::endl;
    debugLog.close();
    //***********

    // 创建圆形碰撞体
    b2Circle circle;
    circle.center = {0.0f, 0.0f};
    circle.radius = GameConfig::getBallRadius() / GameConfig::PIXELS_PER_METER;

    b2ShapeDef shapeDef = b2DefaultShapeDef();
    shapeDef.density = GameConfig::BALL_DENSITY;

    b2ShapeId shapeId = b2CreateCircleShape(physicsBody, &shapeDef, &circle);

    // 设置完全弹性碰撞
    b2Shape_SetRestitution(shapeId, GameConfig::BALL_RESTITUTION);
}

void Shuttlecock::updateFromPhysics()
{
    if (B2_IS_NON_NULL(physicsBody))
    {
        b2Vec2 physicsPos = b2Body_GetPosition(physicsBody);
        b2Vec2 velocity = b2Body_GetLinearVelocity(physicsBody);
        sf::Vector2f newPos = PhysicsWorld::b2ToSf(physicsPos);

        //***********
        static int frameCount = 0;
        frameCount++;
        if (frameCount % 60 == 0) // 每秒输出一次
        {
            std::ofstream debugLog("shuttlecock_debug.txt", std::ios::app);
            debugLog << "Frame " << frameCount << " - Shuttlecock Physics:" << std::endl;
            debugLog << "  Position: (" << physicsPos.x << ", " << physicsPos.y << ")" << std::endl;
            debugLog << "  Velocity: (" << velocity.x << ", " << velocity.y << ")" << std::endl;
            debugLog << "  Render Position: (" << newPos.x << ", " << newPos.y << ")" << std::endl;
            debugLog << "  Body Type: " << (b2Body_GetType(physicsBody) == b2_dynamicBody ? "DYNAMIC" : "NOT_DYNAMIC") << std::endl;
            debugLog << "  Mass: " << b2Body_GetMass(physicsBody) << std::endl;
            debugLog << std::endl;
            debugLog.close();
        }
        //***********

        position = newPos;
        ballShape.setPosition(position);
    }
}

void Shuttlecock::applyAirResistance()
{
    if (B2_IS_NON_NULL(physicsBody))
    {
        b2Vec2 velocity = b2Body_GetLinearVelocity(physicsBody);

        // 羽毛球的空气阻力系数
        float airResistance = 0.98f;

        // 应用空气阻力
        velocity.x *= airResistance;
        velocity.y *= airResistance;

        b2Body_SetLinearVelocity(physicsBody, velocity);
    }
}
