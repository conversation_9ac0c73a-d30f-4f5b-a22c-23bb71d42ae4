#include "../include/HomePage.hpp"
#include "../include/GameConfig.hpp"
#include "../include/GameScene.hpp"
#include <iostream>

HomePage::HomePage(int width, int height, const char *title)
{
    GameConfig::initialize(width, height);
    window = new sf::RenderWindow(sf::VideoMode(width, height), title);
    window->setFramerateLimit(60);
    if (!font.loadFromFile("C:/Windows/Fonts/arial.ttf"))
    {
        // 如果系统字体加载失败，尝试其他字体
        if (!font.loadFromFile("C:/Windows/Fonts/calibri.ttf"))
        {
            // 如果都失败了，使用默认字体（可能显示不正常，但不会崩溃）
            std::cout << "Warning: Could not load font files. Text may not display correctly." << std::endl;
        }
    }
    while (window->isOpen())
    {
        sf::Event event;
        // 监听事件
        while (window->pollEvent(event))
        {
            if (event.type == sf::Event::Closed)
            {
                window->close();
            }
            else if (event.type == sf::Event::KeyPressed)
            {
                if (event.key.code == sf::Keyboard::Space)
                {
                    // 关闭主页窗口，启动游戏
                    window->close();
                    GameScene gameScene;
                }
            }
        }

        setupTexts();
        window->clear();
        render();
        window->display();
    }
}

HomePage::~HomePage()
{
    delete window;
}

void HomePage::setupTexts()
{
    // 设置标题文本
    titleText.setFont(font);
    titleText.setString("Badminton Game");
    titleText.setCharacterSize(48);
    titleText.setFillColor(sf::Color::Black);
    titleText.setPosition(GameConfig::getTitleTextPos().x, GameConfig::getTitleTextPos().y);

    // 设置开始游戏文本
    startText.setFont(font);
    startText.setString("Press SPACE to Start Game");
    startText.setCharacterSize(24);
    startText.setFillColor(sf::Color::Blue);
    startText.setPosition(GameConfig::getStartTextPos().x, GameConfig::getStartTextPos().y);

    // 设置说明文本
    instructionText.setFont(font);
    instructionText.setString("Player 1: A/D-Move, W/S-Racket, Q-Jump\nPlayer 2: Arrow Keys-Move, Up/Down-Racket, RShift-Jump");
    instructionText.setCharacterSize(16);
    instructionText.setFillColor(sf::Color(128, 128, 128)); // 灰色
    instructionText.setPosition(GameConfig::getInstructionTextPos().x, GameConfig::getInstructionTextPos().y);
}

void HomePage::render()
{
    window->draw(titleText);
    window->draw(startText);
    window->draw(instructionText);
}
