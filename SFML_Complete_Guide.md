# SFML 2.6 完整使用指南

## 详细目录

### 1. [SFML基础概念](#1-sfml基础概念)
   - 1.1 [SFML简介](#11-sfml简介)
   - 1.2 [模块结构](#12-模块结构)
   - 1.3 [坐标系统](#13-坐标系统)
   - 1.4 [时间管理](#14-时间管理)

### 2. [窗口管理 (Window)](#2-窗口管理-window)
   - 2.1 [窗口创建](#21-窗口创建)
   - 2.2 [窗口属性](#22-窗口属性)
   - 2.3 [事件处理](#23-事件处理)
   - 2.4 [窗口状态](#24-窗口状态)

### 3. [图形渲染 (Graphics)](#3-图形渲染-graphics)
   - 3.1 [渲染概述](#31-渲染概述)
   - 3.2 [基础图形](#32-基础图形)
   - 3.3 [精灵系统](#33-精灵系统)
   - 3.4 [文本渲染](#34-文本渲染)
   - 3.5 [纹理管理](#35-纹理管理)
   - 3.6 [变换系统](#36-变换系统)
   - 3.7 [视图系统](#37-视图系统)

### 4. [音频系统 (Audio)](#4-音频系统-audio)
   - 4.1 [音频概述](#41-音频概述)
   - 4.2 [音效播放](#42-音效播放)
   - 4.3 [音乐播放](#43-音乐播放)
   - 4.4 [音频流](#44-音频流)
   - 4.5 [3D音频](#45-3d音频)

### 5. [网络通信 (Network)](#5-网络通信-network)
   - 5.1 [网络概述](#51-网络概述)
   - 5.2 [TCP通信](#52-tcp通信)
   - 5.3 [UDP通信](#53-udp通信)
   - 5.4 [HTTP请求](#54-http请求)
   - 5.5 [数据包管理](#55-数据包管理)

### 6. [系统功能 (System)](#6-系统功能-system)
   - 6.1 [时间类](#61-时间类)
   - 6.2 [线程管理](#62-线程管理)
   - 6.3 [文件操作](#63-文件操作)
   - 6.4 [向量数学](#64-向量数学)

### 7. [高级渲染技术](#7-高级渲染技术)
   - 7.1 [着色器](#71-着色器)
   - 7.2 [渲染状态](#72-渲染状态)
   - 7.3 [顶点数组](#73-顶点数组)
   - 7.4 [渲染纹理](#74-渲染纹理)

### 8. [资源管理](#8-资源管理)
   - 8.1 [资源加载](#81-资源加载)
   - 8.2 [资源缓存](#82-资源缓存)
   - 8.3 [内存优化](#83-内存优化)
   - 8.4 [异步加载](#84-异步加载)

### 9. [游戏开发模式](#9-游戏开发模式)
   - 9.1 [游戏循环](#91-游戏循环)
   - 9.2 [场景管理](#92-场景管理)
   - 9.3 [状态机](#93-状态机)
   - 9.4 [实体组件系统](#94-实体组件系统)

### 10. [性能优化](#10-性能优化)
   - 10.1 [渲染优化](#101-渲染优化)
   - 10.2 [内存优化](#102-内存优化)
   - 10.3 [CPU优化](#103-cpu优化)
   - 10.4 [调试工具](#104-调试工具)

### 11. [实际应用示例](#11-实际应用示例)
   - 11.1 [2D平台游戏](#111-2d平台游戏)
   - 11.2 [粒子系统](#112-粒子系统)
   - 11.3 [GUI系统](#113-gui系统)
   - 11.4 [多人游戏](#114-多人游戏)

### 12. [常见问题与解决方案](#12-常见问题与解决方案)
   - 12.1 [渲染问题](#121-渲染问题)
   - 12.2 [性能问题](#122-性能问题)
   - 12.3 [平台兼容性](#123-平台兼容性)
   - 12.4 [调试技巧](#124-调试技巧)

---

## 1. SFML基础概念

### 1.1 SFML简介

SFML (Simple and Fast Multimedia Library) 是一个跨平台的多媒体库，提供：
- **简单易用的API**: 面向对象设计，易于学习
- **跨平台支持**: Windows、Linux、macOS
- **高性能**: 基于OpenGL的硬件加速
- **模块化设计**: 可选择性使用不同模块

#### 主要特性
- 2D图形渲染
- 音频播放和录制
- 网络通信
- 窗口和事件管理
- 多线程支持

### 1.2 模块结构

SFML分为5个主要模块：

#### sf::System
```cpp
#include <SFML/System.hpp>
// 基础系统功能：时间、线程、向量等
```

#### sf::Window  
```cpp
#include <SFML/Window.hpp>
// 窗口管理和事件处理
```

#### sf::Graphics
```cpp
#include <SFML/Graphics.hpp>
// 2D图形渲染 (包含System和Window)
```

#### sf::Audio
```cpp
#include <SFML/Audio.hpp>
// 音频播放和录制 (包含System)
```

#### sf::Network
```cpp
#include <SFML/Network.hpp>
// 网络通信 (包含System)
```

### 1.3 坐标系统

SFML使用标准的计算机图形坐标系：
- **原点 (0,0)**: 屏幕左上角
- **X轴**: 向右为正
- **Y轴**: 向下为正
- **单位**: 像素

#### 坐标转换
```cpp
// 窗口坐标转世界坐标
sf::Vector2i mousePos = sf::Mouse::getPosition(window);
sf::Vector2f worldPos = window.mapPixelToCoords(mousePos);

// 世界坐标转窗口坐标
sf::Vector2i pixelPos = window.mapCoordsToPixel(worldPos);
```

### 1.4 时间管理

SFML提供精确的时间管理：

#### sf::Time类
```cpp
sf::Time time = sf::seconds(1.5f);        // 1.5秒
sf::Time time = sf::milliseconds(500);    // 500毫秒
sf::Time time = sf::microseconds(1000);   // 1000微秒

// 时间转换
float seconds = time.asSeconds();
sf::Int32 milliseconds = time.asMilliseconds();
sf::Int64 microseconds = time.asMicroseconds();
```

#### sf::Clock类
```cpp
sf::Clock clock;
// ... 执行一些操作
sf::Time elapsed = clock.getElapsedTime();
clock.restart();  // 重置并返回经过的时间
```

---

## 2. 窗口管理 (Window)

### 2.1 窗口创建

#### 基础窗口创建
```cpp
#include <SFML/Graphics.hpp>

int main() {
    // 创建窗口
    sf::RenderWindow window(sf::VideoMode(800, 600), "My Game");
    
    // 设置帧率限制
    window.setFramerateLimit(60);
    
    // 启用垂直同步
    window.setVerticalSyncEnabled(true);
    
    // 主循环
    while (window.isOpen()) {
        sf::Event event;
        while (window.pollEvent(event)) {
            if (event.type == sf::Event::Closed)
                window.close();
        }
        
        window.clear();
        // 绘制内容
        window.display();
    }
    
    return 0;
}
```

#### 高级窗口配置
```cpp
// 自定义窗口设置
sf::ContextSettings settings;
settings.depthBits = 24;          // 深度缓冲
settings.stencilBits = 8;         // 模板缓冲
settings.antialiasingLevel = 4;   // 抗锯齿
settings.majorVersion = 3;        // OpenGL主版本
settings.minorVersion = 0;        // OpenGL次版本

sf::RenderWindow window(
    sf::VideoMode(1024, 768),
    "Advanced Window",
    sf::Style::Default,
    settings
);
```

### 2.2 窗口属性

#### 窗口样式
```cpp
// 不同的窗口样式
sf::Style::None;         // 无边框
sf::Style::Titlebar;     // 标题栏
sf::Style::Resize;       // 可调整大小
sf::Style::Close;        // 关闭按钮
sf::Style::Fullscreen;   // 全屏
sf::Style::Default;      // 默认 (Titlebar | Resize | Close)

// 组合样式
sf::Uint32 style = sf::Style::Titlebar | sf::Style::Close;
```

#### 窗口操作
```cpp
// 窗口位置和大小
window.setPosition(sf::Vector2i(100, 100));
window.setSize(sf::Vector2u(800, 600));
sf::Vector2i position = window.getPosition();
sf::Vector2u size = window.getSize();

// 窗口标题和图标
window.setTitle("New Title");
sf::Image icon;
if (icon.loadFromFile("icon.png")) {
    window.setIcon(icon.getSize().x, icon.getSize().y, icon.getPixelsPtr());
}

// 窗口可见性
window.setVisible(true);
window.requestFocus();

### 2.3 事件处理

#### 事件类型
```cpp
// 主要事件类型
sf::Event::Closed;              // 窗口关闭
sf::Event::Resized;             // 窗口大小改变
sf::Event::LostFocus;           // 失去焦点
sf::Event::GainedFocus;         // 获得焦点
sf::Event::TextEntered;         // 文本输入
sf::Event::KeyPressed;          // 按键按下
sf::Event::KeyReleased;         // 按键释放
sf::Event::MouseWheelScrolled;  // 鼠标滚轮
sf::Event::MouseButtonPressed;  // 鼠标按下
sf::Event::MouseButtonReleased; // 鼠标释放
sf::Event::MouseMoved;          // 鼠标移动
sf::Event::MouseEntered;        // 鼠标进入窗口
sf::Event::MouseLeft;           // 鼠标离开窗口
sf::Event::JoystickButtonPressed; // 手柄按钮
sf::Event::JoystickMoved;       // 手柄摇杆
sf::Event::JoystickConnected;   // 手柄连接
```

#### 完整事件处理示例
```cpp
void handleEvents(sf::RenderWindow& window) {
    sf::Event event;
    while (window.pollEvent(event)) {
        switch (event.type) {
            case sf::Event::Closed:
                window.close();
                break;

            case sf::Event::Resized:
                printf("Window resized: %dx%d\n",
                       event.size.width, event.size.height);
                // 调整视图
                sf::FloatRect visibleArea(0, 0, event.size.width, event.size.height);
                window.setView(sf::View(visibleArea));
                break;

            case sf::Event::KeyPressed:
                if (event.key.code == sf::Keyboard::Escape) {
                    window.close();
                } else if (event.key.code == sf::Keyboard::F11) {
                    // 切换全屏
                    toggleFullscreen(window);
                }
                break;

            case sf::Event::MouseButtonPressed:
                if (event.mouseButton.button == sf::Mouse::Left) {
                    printf("Left click at (%d, %d)\n",
                           event.mouseButton.x, event.mouseButton.y);
                }
                break;

            case sf::Event::MouseWheelScrolled:
                if (event.mouseWheelScroll.wheel == sf::Mouse::VerticalWheel) {
                    printf("Wheel scrolled: %f\n", event.mouseWheelScroll.delta);
                }
                break;

            case sf::Event::TextEntered:
                if (event.text.unicode < 128) {
                    printf("Text entered: %c\n", static_cast<char>(event.text.unicode));
                }
                break;
        }
    }
}
```

#### 实时输入检测
```cpp
void handleRealTimeInput() {
    // 键盘状态
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Left)) {
        // 左键被按住
    }
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Space)) {
        // 空格键被按住
    }

    // 鼠标状态
    if (sf::Mouse::isButtonPressed(sf::Mouse::Left)) {
        sf::Vector2i mousePos = sf::Mouse::getPosition();
        // 左键被按住，获取鼠标位置
    }

    // 手柄状态
    if (sf::Joystick::isConnected(0)) {
        float x = sf::Joystick::getAxisPosition(0, sf::Joystick::X);
        float y = sf::Joystick::getAxisPosition(0, sf::Joystick::Y);
        // 处理手柄输入
    }
}
```

### 2.4 窗口状态

#### 窗口状态查询
```cpp
// 检查窗口状态
bool isOpen = window.isOpen();
bool hasFocus = window.hasFocus();

// 获取窗口设置
sf::ContextSettings settings = window.getSettings();
printf("Depth bits: %d\n", settings.depthBits);
printf("Antialiasing: %d\n", settings.antialiasingLevel);
```

#### 全屏切换
```cpp
bool isFullscreen = false;
sf::VideoMode desktopMode = sf::VideoMode::getDesktopMode();

void toggleFullscreen(sf::RenderWindow& window) {
    window.close();

    if (isFullscreen) {
        // 切换到窗口模式
        window.create(sf::VideoMode(800, 600), "Game", sf::Style::Default);
        isFullscreen = false;
    } else {
        // 切换到全屏模式
        window.create(desktopMode, "Game", sf::Style::Fullscreen);
        isFullscreen = true;
    }
}
```

---

## 3. 图形渲染 (Graphics)

### 3.1 渲染概述

SFML的渲染系统基于OpenGL，提供：
- **立即模式渲染**: 简单直接的绘制方式
- **硬件加速**: 利用GPU进行渲染
- **状态管理**: 自动管理OpenGL状态
- **批处理**: 自动优化绘制调用

#### 基础渲染循环
```cpp
while (window.isOpen()) {
    // 1. 处理事件
    handleEvents(window);

    // 2. 更新游戏逻辑
    updateGame(deltaTime);

    // 3. 渲染
    window.clear(sf::Color::Black);  // 清屏

    // 绘制所有对象
    window.draw(sprite);
    window.draw(text);
    window.draw(shape);

    window.display();  // 显示到屏幕
}
```

### 3.2 基础图形

#### sf::RectangleShape
```cpp
// 创建矩形
sf::RectangleShape rectangle(sf::Vector2f(100.0f, 50.0f));
rectangle.setPosition(10.0f, 20.0f);
rectangle.setFillColor(sf::Color::Red);
rectangle.setOutlineColor(sf::Color::Blue);
rectangle.setOutlineThickness(2.0f);

// 设置原点 (用于旋转和缩放)
rectangle.setOrigin(50.0f, 25.0f);  // 中心点
rectangle.setRotation(45.0f);        // 旋转45度
rectangle.setScale(2.0f, 1.5f);      // 缩放

window.draw(rectangle);
```

#### sf::CircleShape
```cpp
// 创建圆形
sf::CircleShape circle(50.0f);  // 半径50像素
circle.setPosition(100.0f, 100.0f);
circle.setFillColor(sf::Color::Green);
circle.setPointCount(30);  // 设置顶点数 (影响圆的平滑度)

// 创建多边形 (使用CircleShape)
sf::CircleShape triangle(50.0f, 3);  // 三角形
sf::CircleShape hexagon(50.0f, 6);   // 六边形

window.draw(circle);
```

#### sf::ConvexShape
```cpp
// 创建自定义凸多边形
sf::ConvexShape convex;
convex.setPointCount(5);  // 五边形

// 设置顶点
convex.setPoint(0, sf::Vector2f(0.0f, 0.0f));
convex.setPoint(1, sf::Vector2f(50.0f, 10.0f));
convex.setPoint(2, sf::Vector2f(100.0f, 50.0f));
convex.setPoint(3, sf::Vector2f(50.0f, 100.0f));
convex.setPoint(4, sf::Vector2f(0.0f, 50.0f));

convex.setFillColor(sf::Color::Yellow);
window.draw(convex);

### 3.3 精灵系统

#### sf::Texture和sf::Sprite
```cpp
// 加载纹理
sf::Texture texture;
if (!texture.loadFromFile("player.png")) {
    // 处理加载失败
    printf("Failed to load texture\n");
    return -1;
}

// 创建精灵
sf::Sprite sprite;
sprite.setTexture(texture);
sprite.setPosition(100.0f, 100.0f);

// 设置纹理矩形 (裁剪)
sprite.setTextureRect(sf::IntRect(0, 0, 32, 32));  // 32x32像素区域

// 变换
sprite.setOrigin(16.0f, 16.0f);  // 设置中心点
sprite.setScale(2.0f, 2.0f);     // 放大2倍
sprite.setRotation(45.0f);       // 旋转45度

// 颜色调制
sprite.setColor(sf::Color(255, 255, 255, 128));  // 半透明

window.draw(sprite);
```

#### 精灵动画
```cpp
class SpriteAnimation {
private:
    sf::Sprite sprite;
    sf::Texture texture;
    std::vector<sf::IntRect> frames;
    int currentFrame;
    sf::Clock animationClock;
    sf::Time frameTime;

public:
    SpriteAnimation(const std::string& texturePath, sf::Time frameTime)
        : currentFrame(0), frameTime(frameTime) {
        texture.loadFromFile(texturePath);
        sprite.setTexture(texture);
    }

    void addFrame(sf::IntRect rect) {
        frames.push_back(rect);
    }

    void update() {
        if (animationClock.getElapsedTime() >= frameTime) {
            currentFrame = (currentFrame + 1) % frames.size();
            sprite.setTextureRect(frames[currentFrame]);
            animationClock.restart();
        }
    }

    void draw(sf::RenderWindow& window) {
        window.draw(sprite);
    }

    sf::Sprite& getSprite() { return sprite; }
};

// 使用示例
SpriteAnimation playerAnim("player_walk.png", sf::milliseconds(100));
playerAnim.addFrame(sf::IntRect(0, 0, 32, 32));
playerAnim.addFrame(sf::IntRect(32, 0, 32, 32));
playerAnim.addFrame(sf::IntRect(64, 0, 32, 32));
playerAnim.addFrame(sf::IntRect(96, 0, 32, 32));

// 在游戏循环中
playerAnim.update();
playerAnim.draw(window);
```

### 3.4 文本渲染

#### sf::Font和sf::Text
```cpp
// 加载字体
sf::Font font;
if (!font.loadFromFile("arial.ttf")) {
    // 处理字体加载失败
    printf("Failed to load font\n");
    return -1;
}

// 创建文本
sf::Text text;
text.setFont(font);
text.setString("Hello, SFML!");
text.setCharacterSize(24);           // 字体大小
text.setFillColor(sf::Color::White); // 文字颜色
text.setStyle(sf::Text::Bold | sf::Text::Underlined); // 样式

// 位置和对齐
text.setPosition(100.0f, 50.0f);

// 获取文本边界
sf::FloatRect textBounds = text.getLocalBounds();
text.setOrigin(textBounds.left + textBounds.width / 2.0f,
               textBounds.top + textBounds.height / 2.0f);

window.draw(text);
```

#### 文本样式和效果
```cpp
// 文本样式
sf::Text::Regular;     // 常规
sf::Text::Bold;        // 粗体
sf::Text::Italic;      // 斜体
sf::Text::Underlined;  // 下划线
sf::Text::StrikeThrough; // 删除线

// 组合样式
text.setStyle(sf::Text::Bold | sf::Text::Italic);

// 文本颜色和轮廓
text.setFillColor(sf::Color::Red);
text.setOutlineColor(sf::Color::Black);
text.setOutlineThickness(2.0f);

// 字符间距和行间距
text.setLetterSpacing(1.5f);  // 字符间距
text.setLineSpacing(1.2f);    // 行间距
```

#### 动态文本
```cpp
class DynamicText {
private:
    sf::Text text;
    sf::Font font;
    std::string baseString;
    sf::Clock updateClock;

public:
    DynamicText(const std::string& fontPath) {
        font.loadFromFile(fontPath);
        text.setFont(font);
        text.setCharacterSize(20);
        text.setFillColor(sf::Color::White);
    }

    void setBaseString(const std::string& str) {
        baseString = str;
    }

    void updateScore(int score) {
        text.setString(baseString + std::to_string(score));
    }

    void updateTimer(float seconds) {
        int minutes = static_cast<int>(seconds) / 60;
        int secs = static_cast<int>(seconds) % 60;
        char timeStr[16];
        sprintf(timeStr, "%02d:%02d", minutes, secs);
        text.setString(baseString + timeStr);
    }

    void setPosition(float x, float y) {
        text.setPosition(x, y);
    }

    void draw(sf::RenderWindow& window) {
        window.draw(text);
    }
};

// 使用示例
DynamicText scoreText("arial.ttf");
scoreText.setBaseString("Score: ");
scoreText.setPosition(10, 10);

DynamicText timerText("arial.ttf");
timerText.setBaseString("Time: ");
timerText.setPosition(10, 40);

// 在游戏循环中
scoreText.updateScore(playerScore);
timerText.updateTimer(gameTime);
scoreText.draw(window);
timerText.draw(window);

### 3.5 纹理管理

#### 纹理加载和属性
```cpp
sf::Texture texture;

// 从文件加载
if (!texture.loadFromFile("texture.png")) {
    // 处理错误
}

// 从内存加载
unsigned char* data = ...; // 图像数据
texture.loadFromMemory(data, dataSize);

// 从图像加载
sf::Image image;
image.loadFromFile("image.png");
texture.loadFromImage(image);

// 纹理属性
sf::Vector2u size = texture.getSize();
printf("Texture size: %dx%d\n", size.x, size.y);

// 纹理设置
texture.setSmooth(true);    // 平滑过滤
texture.setRepeated(true);  // 重复模式
```

#### 纹理管理器
```cpp
class TextureManager {
private:
    std::map<std::string, sf::Texture> textures;

public:
    bool loadTexture(const std::string& name, const std::string& filename) {
        sf::Texture texture;
        if (texture.loadFromFile(filename)) {
            textures[name] = std::move(texture);
            return true;
        }
        return false;
    }

    sf::Texture& getTexture(const std::string& name) {
        auto it = textures.find(name);
        if (it != textures.end()) {
            return it->second;
        }
        // 返回默认纹理或抛出异常
        static sf::Texture defaultTexture;
        return defaultTexture;
    }

    void unloadTexture(const std::string& name) {
        textures.erase(name);
    }

    void unloadAll() {
        textures.clear();
    }

    size_t getTextureCount() const {
        return textures.size();
    }
};

// 使用示例
TextureManager textureManager;
textureManager.loadTexture("player", "player.png");
textureManager.loadTexture("enemy", "enemy.png");
textureManager.loadTexture("background", "bg.png");

sf::Sprite playerSprite;
playerSprite.setTexture(textureManager.getTexture("player"));
```

#### 纹理图集 (Texture Atlas)
```cpp
class TextureAtlas {
private:
    sf::Texture atlas;
    std::map<std::string, sf::IntRect> regions;

public:
    bool loadFromFile(const std::string& filename) {
        return atlas.loadFromFile(filename);
    }

    void addRegion(const std::string& name, sf::IntRect rect) {
        regions[name] = rect;
    }

    sf::IntRect getRegion(const std::string& name) const {
        auto it = regions.find(name);
        if (it != regions.end()) {
            return it->second;
        }
        return sf::IntRect(0, 0, 0, 0);
    }

    const sf::Texture& getTexture() const {
        return atlas;
    }

    sf::Sprite createSprite(const std::string& regionName) const {
        sf::Sprite sprite;
        sprite.setTexture(atlas);
        sprite.setTextureRect(getRegion(regionName));
        return sprite;
    }
};

// 使用示例
TextureAtlas gameAtlas;
gameAtlas.loadFromFile("game_atlas.png");
gameAtlas.addRegion("player_idle", sf::IntRect(0, 0, 32, 32));
gameAtlas.addRegion("player_walk1", sf::IntRect(32, 0, 32, 32));
gameAtlas.addRegion("player_walk2", sf::IntRect(64, 0, 32, 32));

sf::Sprite playerSprite = gameAtlas.createSprite("player_idle");
```

### 3.6 变换系统

#### sf::Transform
```cpp
// 创建变换矩阵
sf::Transform transform;

// 基础变换
transform.translate(100.0f, 50.0f);  // 平移
transform.rotate(45.0f);             // 旋转45度
transform.scale(2.0f, 1.5f);         // 缩放

// 绕指定点旋转
transform.rotate(30.0f, 50.0f, 50.0f);

// 组合变换
sf::Transform combined = transform1 * transform2;

// 应用变换到点
sf::Vector2f point(10.0f, 20.0f);
sf::Vector2f transformedPoint = transform.transformPoint(point);

// 应用变换到矩形
sf::FloatRect rect(0, 0, 100, 50);
sf::FloatRect transformedRect = transform.transformRect(rect);
```

#### sf::Transformable
```cpp
// 所有可绘制对象都继承自Transformable
sf::Sprite sprite;
sf::Text text;
sf::RectangleShape shape;

// 统一的变换接口
void transformObject(sf::Transformable& obj) {
    obj.setPosition(100.0f, 100.0f);
    obj.setRotation(45.0f);
    obj.setScale(2.0f, 2.0f);
    obj.setOrigin(50.0f, 50.0f);

    // 相对变换
    obj.move(10.0f, 5.0f);
    obj.rotate(15.0f);
    obj.scale(1.1f, 1.1f);
}

// 获取变换信息
sf::Vector2f position = sprite.getPosition();
float rotation = sprite.getRotation();
sf::Vector2f scale = sprite.getScale();
sf::Vector2f origin = sprite.getOrigin();

// 获取变换矩阵
sf::Transform transform = sprite.getTransform();
sf::Transform inverseTransform = sprite.getInverseTransform();
```

#### 自定义变换类
```cpp
class GameObject : public sf::Transformable, public sf::Drawable {
private:
    sf::Sprite sprite;
    sf::Vector2f velocity;
    float angularVelocity;

public:
    GameObject(const sf::Texture& texture) {
        sprite.setTexture(texture);
    }

    void setVelocity(sf::Vector2f vel) {
        velocity = vel;
    }

    void setAngularVelocity(float angVel) {
        angularVelocity = angVel;
    }

    void update(sf::Time deltaTime) {
        float dt = deltaTime.asSeconds();

        // 更新位置
        move(velocity * dt);

        // 更新旋转
        rotate(angularVelocity * dt);

        // 更新精灵变换
        sprite.setPosition(getPosition());
        sprite.setRotation(getRotation());
        sprite.setScale(getScale());
        sprite.setOrigin(getOrigin());
    }

private:
    virtual void draw(sf::RenderTarget& target, sf::RenderStates states) const override {
        // 应用对象的变换
        states.transform *= getTransform();
        target.draw(sprite, states);
    }
};

// 使用示例
GameObject player(playerTexture);
player.setPosition(100, 100);
player.setVelocity(sf::Vector2f(50, 0));  // 每秒移动50像素
player.setAngularVelocity(90);            // 每秒旋转90度

// 在游戏循环中
player.update(deltaTime);
window.draw(player);

### 3.7 视图系统

#### sf::View基础
```cpp
// 创建视图
sf::View view;

// 设置视图中心和大小
view.setCenter(400.0f, 300.0f);      // 视图中心
view.setSize(800.0f, 600.0f);        // 视图大小

// 设置视口 (屏幕上的显示区域)
view.setViewport(sf::FloatRect(0.0f, 0.0f, 1.0f, 1.0f));  // 全屏
view.setViewport(sf::FloatRect(0.0f, 0.0f, 0.5f, 1.0f));  // 左半屏

// 应用视图
window.setView(view);

// 恢复默认视图
window.setView(window.getDefaultView());
```

#### 摄像机系统
```cpp
class Camera {
private:
    sf::View view;
    sf::Vector2f target;
    sf::Vector2f position;
    float smoothness;
    sf::Vector2f bounds;

public:
    Camera(sf::Vector2f size, float smoothness = 0.1f)
        : smoothness(smoothness) {
        view.setSize(size);
        bounds = sf::Vector2f(0, 0);  // 无边界限制
    }

    void setTarget(sf::Vector2f newTarget) {
        target = newTarget;
    }

    void setBounds(sf::Vector2f worldBounds) {
        bounds = worldBounds;
    }

    void update(sf::Time deltaTime) {
        // 平滑跟随
        sf::Vector2f direction = target - position;
        position += direction * smoothness;

        // 边界限制
        if (bounds.x > 0 && bounds.y > 0) {
            sf::Vector2f halfSize = view.getSize() / 2.0f;

            if (position.x - halfSize.x < 0)
                position.x = halfSize.x;
            if (position.y - halfSize.y < 0)
                position.y = halfSize.y;
            if (position.x + halfSize.x > bounds.x)
                position.x = bounds.x - halfSize.x;
            if (position.y + halfSize.y > bounds.y)
                position.y = bounds.y - halfSize.y;
        }

        view.setCenter(position);
    }

    void shake(float intensity, sf::Time duration) {
        // 实现摄像机震动效果
        static sf::Clock shakeClock;
        if (shakeClock.getElapsedTime() < duration) {
            float progress = shakeClock.getElapsedTime().asSeconds() / duration.asSeconds();
            float currentIntensity = intensity * (1.0f - progress);

            float offsetX = (rand() % 200 - 100) * currentIntensity / 100.0f;
            float offsetY = (rand() % 200 - 100) * currentIntensity / 100.0f;

            view.setCenter(position.x + offsetX, position.y + offsetY);
        } else {
            view.setCenter(position);
        }
    }

    void zoom(float factor) {
        view.zoom(factor);
    }

    const sf::View& getView() const {
        return view;
    }

    sf::Vector2f getPosition() const {
        return position;
    }
};

// 使用示例
Camera camera(sf::Vector2f(800, 600), 0.05f);
camera.setBounds(sf::Vector2f(2000, 1500));  // 世界边界

// 在游戏循环中
camera.setTarget(player.getPosition());
camera.update(deltaTime);
window.setView(camera.getView());
```

#### 分屏视图
```cpp
class SplitScreenManager {
private:
    std::vector<sf::View> views;
    std::vector<sf::Vector2f> playerPositions;

public:
    void setupTwoPlayerSplit(sf::Vector2u windowSize) {
        views.clear();
        views.resize(2);

        // 左半屏
        views[0].setSize(windowSize.x / 2.0f, windowSize.y);
        views[0].setViewport(sf::FloatRect(0.0f, 0.0f, 0.5f, 1.0f));

        // 右半屏
        views[1].setSize(windowSize.x / 2.0f, windowSize.y);
        views[1].setViewport(sf::FloatRect(0.5f, 0.0f, 0.5f, 1.0f));
    }

    void updatePlayerView(int playerIndex, sf::Vector2f position) {
        if (playerIndex < views.size()) {
            views[playerIndex].setCenter(position);
        }
    }

    void renderPlayer(sf::RenderWindow& window, int playerIndex,
                     const std::vector<sf::Drawable*>& objects) {
        if (playerIndex < views.size()) {
            window.setView(views[playerIndex]);

            for (auto* obj : objects) {
                window.draw(*obj);
            }
        }
    }
};
```

---

## 4. 音频系统 (Audio)

### 4.1 音频概述

SFML音频系统提供：
- **音效播放**: 短音频片段，适合游戏音效
- **音乐播放**: 长音频文件，适合背景音乐
- **3D音频**: 空间音频效果
- **音频录制**: 麦克风录音功能

#### 支持的音频格式
- WAV (推荐用于音效)
- OGG Vorbis (推荐用于音乐)
- FLAC
- MP3 (部分支持)

### 4.2 音效播放

#### sf::SoundBuffer和sf::Sound
```cpp
// 加载音效
sf::SoundBuffer buffer;
if (!buffer.loadFromFile("jump.wav")) {
    printf("Failed to load sound\n");
    return -1;
}

// 创建音效对象
sf::Sound sound;
sound.setBuffer(buffer);

// 播放控制
sound.play();
sound.pause();
sound.stop();

// 音效属性
sound.setVolume(50.0f);        // 音量 (0-100)
sound.setPitch(1.5f);          // 音调 (0.5 = 半速, 2.0 = 双速)
sound.setLoop(false);          // 是否循环

// 检查播放状态
sf::Sound::Status status = sound.getStatus();
if (status == sf::Sound::Playing) {
    // 正在播放
}
```

#### 音效管理器
```cpp
class SoundManager {
private:
    std::map<std::string, sf::SoundBuffer> buffers;
    std::vector<sf::Sound> sounds;
    float masterVolume;

public:
    SoundManager() : masterVolume(100.0f) {}

    bool loadSound(const std::string& name, const std::string& filename) {
        sf::SoundBuffer buffer;
        if (buffer.loadFromFile(filename)) {
            buffers[name] = std::move(buffer);
            return true;
        }
        return false;
    }

    void playSound(const std::string& name, float volume = 100.0f, float pitch = 1.0f) {
        auto it = buffers.find(name);
        if (it != buffers.end()) {
            // 清理已停止的音效
            sounds.erase(
                std::remove_if(sounds.begin(), sounds.end(),
                    [](const sf::Sound& s) { return s.getStatus() == sf::Sound::Stopped; }),
                sounds.end()
            );

            // 创建新音效
            sounds.emplace_back();
            sf::Sound& sound = sounds.back();
            sound.setBuffer(it->second);
            sound.setVolume(volume * masterVolume / 100.0f);
            sound.setPitch(pitch);
            sound.play();
        }
    }

    void setMasterVolume(float volume) {
        masterVolume = volume;
        for (auto& sound : sounds) {
            sound.setVolume(sound.getVolume() * masterVolume / 100.0f);
        }
    }

    void stopAllSounds() {
        for (auto& sound : sounds) {
            sound.stop();
        }
        sounds.clear();
    }
};

// 使用示例
SoundManager soundManager;
soundManager.loadSound("jump", "jump.wav");
soundManager.loadSound("coin", "coin.wav");
soundManager.loadSound("explosion", "explosion.wav");

// 播放音效
soundManager.playSound("jump");
soundManager.playSound("coin", 80.0f);  // 80%音量
soundManager.playSound("explosion", 100.0f, 0.8f);  // 低音调

### 4.3 音乐播放

#### sf::Music
```cpp
// 加载和播放音乐
sf::Music music;
if (!music.openFromFile("background.ogg")) {
    printf("Failed to load music\n");
    return -1;
}

// 播放控制
music.play();
music.pause();
music.stop();

// 音乐属性
music.setVolume(75.0f);        // 音量
music.setPitch(1.0f);          // 音调
music.setLoop(true);           // 循环播放

// 播放位置控制
music.setPlayingOffset(sf::seconds(30.0f));  // 从30秒开始播放
sf::Time currentTime = music.getPlayingOffset();

// 获取音乐信息
sf::Time duration = music.getDuration();
unsigned int sampleRate = music.getSampleRate();
unsigned int channelCount = music.getChannelCount();
```

#### 音乐管理器
```cpp
class MusicManager {
private:
    sf::Music currentMusic;
    std::string currentTrack;
    float volume;
    bool isFading;
    sf::Clock fadeTimer;
    sf::Time fadeDuration;
    float fadeStartVolume;
    float fadeTargetVolume;

public:
    MusicManager() : volume(100.0f), isFading(false) {}

    bool playMusic(const std::string& filename, bool loop = true) {
        if (currentMusic.openFromFile(filename)) {
            currentMusic.setLoop(loop);
            currentMusic.setVolume(volume);
            currentMusic.play();
            currentTrack = filename;
            return true;
        }
        return false;
    }

    void stopMusic() {
        currentMusic.stop();
        currentTrack.clear();
    }

    void pauseMusic() {
        currentMusic.pause();
    }

    void resumeMusic() {
        currentMusic.play();
    }

    void setVolume(float vol) {
        volume = vol;
        if (!isFading) {
            currentMusic.setVolume(volume);
        }
    }

    void fadeIn(sf::Time duration) {
        startFade(0.0f, volume, duration);
    }

    void fadeOut(sf::Time duration) {
        startFade(volume, 0.0f, duration);
    }

    void crossFade(const std::string& newTrack, sf::Time duration) {
        // 简化版交叉淡入淡出
        fadeOut(duration);
        // 在实际实现中，需要在淡出完成后播放新音乐
    }

    void update() {
        if (isFading) {
            float progress = fadeTimer.getElapsedTime().asSeconds() / fadeDuration.asSeconds();
            if (progress >= 1.0f) {
                currentMusic.setVolume(fadeTargetVolume);
                isFading = false;
                if (fadeTargetVolume == 0.0f) {
                    currentMusic.stop();
                }
            } else {
                float currentVol = fadeStartVolume + (fadeTargetVolume - fadeStartVolume) * progress;
                currentMusic.setVolume(currentVol);
            }
        }
    }

private:
    void startFade(float startVol, float targetVol, sf::Time duration) {
        fadeStartVolume = startVol;
        fadeTargetVolume = targetVol;
        fadeDuration = duration;
        isFading = true;
        fadeTimer.restart();
    }
};

// 使用示例
MusicManager musicManager;
musicManager.playMusic("menu_music.ogg");
musicManager.setVolume(80.0f);

// 切换音乐时淡出
musicManager.fadeOut(sf::seconds(2.0f));
// 然后播放新音乐
musicManager.playMusic("game_music.ogg");
musicManager.fadeIn(sf::seconds(2.0f));
```

### 4.4 3D音频

#### 空间音频
```cpp
// 设置监听者 (玩家)
sf::Listener::setPosition(playerX, 0.0f, playerY);
sf::Listener::setDirection(dirX, 0.0f, dirY);
sf::Listener::setUpVector(0.0f, 1.0f, 0.0f);
sf::Listener::setGlobalVolume(100.0f);

// 3D音效
sf::Sound sound3D;
sound3D.setBuffer(buffer);
sound3D.setPosition(enemyX, 0.0f, enemyY);  // 敌人位置
sound3D.setMinDistance(50.0f);              // 最小距离
sound3D.setAttenuation(2.0f);               // 衰减因子
sound3D.play();

// 移动音源
void updateEnemySound(sf::Sound& sound, sf::Vector2f enemyPos) {
    sound.setPosition(enemyPos.x, 0.0f, enemyPos.y);
}
```

---

## 5. 网络通信 (Network)

### 5.1 网络概述

SFML网络模块提供：
- **TCP连接**: 可靠的数据传输
- **UDP通信**: 快速的数据传输
- **HTTP请求**: Web通信
- **数据包**: 结构化数据传输

### 5.2 TCP通信

#### TCP服务器
```cpp
#include <SFML/Network.hpp>

class TCPServer {
private:
    sf::TcpListener listener;
    std::vector<std::unique_ptr<sf::TcpSocket>> clients;
    sf::SocketSelector selector;
    bool running;

public:
    TCPServer() : running(false) {}

    bool start(unsigned short port) {
        if (listener.listen(port) != sf::Socket::Done) {
            printf("Failed to bind to port %d\n", port);
            return false;
        }

        selector.add(listener);
        running = true;
        printf("Server listening on port %d\n", port);
        return true;
    }

    void update() {
        if (!running) return;

        if (selector.wait(sf::milliseconds(10))) {
            // 检查新连接
            if (selector.isReady(listener)) {
                auto client = std::make_unique<sf::TcpSocket>();
                if (listener.accept(*client) == sf::Socket::Done) {
                    printf("New client connected\n");
                    selector.add(*client);
                    clients.push_back(std::move(client));
                }
            }

            // 检查客户端消息
            for (auto it = clients.begin(); it != clients.end();) {
                if (selector.isReady(**it)) {
                    sf::Packet packet;
                    sf::Socket::Status status = (*it)->receive(packet);

                    if (status == sf::Socket::Done) {
                        handleClientMessage(packet, **it);
                        ++it;
                    } else if (status == sf::Socket::Disconnected) {
                        printf("Client disconnected\n");
                        selector.remove(**it);
                        it = clients.erase(it);
                    } else {
                        ++it;
                    }
                }
            }
        }
    }

    void broadcast(sf::Packet& packet) {
        for (auto& client : clients) {
            client->send(packet);
        }
    }

    void stop() {
        running = false;
        listener.close();
        clients.clear();
    }

private:
    void handleClientMessage(sf::Packet& packet, sf::TcpSocket& client) {
        std::string message;
        if (packet >> message) {
            printf("Received: %s\n", message.c_str());

            // 回显消息给所有客户端
            sf::Packet response;
            response << message;
            broadcast(response);
        }
    }
};
```

#### TCP客户端
```cpp
class TCPClient {
private:
    sf::TcpSocket socket;
    bool connected;

public:
    TCPClient() : connected(false) {}

    bool connect(const std::string& serverIP, unsigned short port) {
        sf::Socket::Status status = socket.connect(serverIP, port);
        if (status == sf::Socket::Done) {
            connected = true;
            printf("Connected to server\n");
            return true;
        }
        printf("Failed to connect to server\n");
        return false;
    }

    void disconnect() {
        socket.disconnect();
        connected = false;
    }

    bool sendMessage(const std::string& message) {
        if (!connected) return false;

        sf::Packet packet;
        packet << message;
        return socket.send(packet) == sf::Socket::Done;
    }

    bool receiveMessage(std::string& message) {
        if (!connected) return false;

        sf::Packet packet;
        sf::Socket::Status status = socket.receive(packet);

        if (status == sf::Socket::Done) {
            return packet >> message;
        } else if (status == sf::Socket::Disconnected) {
            connected = false;
        }
        return false;
    }

    bool isConnected() const {
        return connected;
    }
};

// 使用示例
TCPClient client;
if (client.connect("127.0.0.1", 12345)) {
    client.sendMessage("Hello Server!");

    std::string response;
    if (client.receiveMessage(response)) {
        printf("Server replied: %s\n", response.c_str());
    }
}

### 5.3 UDP通信

#### UDP套接字
```cpp
// UDP发送端
sf::UdpSocket senderSocket;
sf::IpAddress recipient = "127.0.0.1";
unsigned short port = 12345;

std::string message = "Hello UDP!";
if (senderSocket.send(message.c_str(), message.size(), recipient, port) != sf::Socket::Done) {
    printf("Failed to send UDP message\n");
}

// UDP接收端
sf::UdpSocket receiverSocket;
if (receiverSocket.bind(12345) != sf::Socket::Done) {
    printf("Failed to bind UDP socket\n");
}

char buffer[1024];
std::size_t received;
sf::IpAddress sender;
unsigned short senderPort;

if (receiverSocket.receive(buffer, sizeof(buffer), received, sender, senderPort) == sf::Socket::Done) {
    printf("Received from %s:%d: %s\n", sender.toString().c_str(), senderPort, buffer);
}
```

#### UDP游戏网络
```cpp
class UDPGameNetwork {
private:
    sf::UdpSocket socket;
    unsigned short localPort;
    std::vector<sf::IpAddress> peers;
    std::vector<unsigned short> peerPorts;

public:
    bool initialize(unsigned short port) {
        localPort = port;
        return socket.bind(port) == sf::Socket::Done;
    }

    void addPeer(sf::IpAddress address, unsigned short port) {
        peers.push_back(address);
        peerPorts.push_back(port);
    }

    void sendGameState(const std::string& gameState) {
        for (size_t i = 0; i < peers.size(); ++i) {
            socket.send(gameState.c_str(), gameState.size(), peers[i], peerPorts[i]);
        }
    }

    bool receiveGameState(std::string& gameState) {
        char buffer[1024];
        std::size_t received;
        sf::IpAddress sender;
        unsigned short senderPort;

        if (socket.receive(buffer, sizeof(buffer), received, sender, senderPort) == sf::Socket::Done) {
            gameState = std::string(buffer, received);
            return true;
        }
        return false;
    }
};
```

### 5.4 HTTP请求

#### 基础HTTP客户端
```cpp
// GET请求
sf::Http http("http://example.com");
sf::Http::Request request("/api/data", sf::Http::Request::Get);
request.setField("User-Agent", "SFML Game");

sf::Http::Response response = http.sendRequest(request);
if (response.getStatus() == sf::Http::Response::Ok) {
    std::string body = response.getBody();
    printf("Response: %s\n", body.c_str());
}

// POST请求
sf::Http::Request postRequest("/api/submit", sf::Http::Request::Post);
postRequest.setField("Content-Type", "application/json");
postRequest.setBody("{\"score\": 1000, \"player\": \"Alice\"}");

sf::Http::Response postResponse = http.sendRequest(postRequest);
```

---

## 6. 系统功能 (System)

### 6.1 时间类

#### sf::Time和sf::Clock详解
```cpp
// 时间创建
sf::Time time1 = sf::seconds(5.5f);
sf::Time time2 = sf::milliseconds(1500);
sf::Time time3 = sf::microseconds(2000000);

// 时间运算
sf::Time total = time1 + time2;
sf::Time difference = time1 - time2;
sf::Time scaled = time1 * 2.0f;
sf::Time divided = time1 / 3.0f;

// 时间比较
if (time1 > time2) {
    printf("time1 is longer\n");
}

// 高精度计时器
class PerformanceTimer {
private:
    sf::Clock clock;
    std::vector<sf::Time> samples;
    size_t maxSamples;

public:
    PerformanceTimer(size_t maxSamples = 100) : maxSamples(maxSamples) {}

    void start() {
        clock.restart();
    }

    void end() {
        sf::Time elapsed = clock.getElapsedTime();
        samples.push_back(elapsed);

        if (samples.size() > maxSamples) {
            samples.erase(samples.begin());
        }
    }

    float getAverageMs() const {
        if (samples.empty()) return 0.0f;

        sf::Time total = sf::Time::Zero;
        for (const auto& sample : samples) {
            total += sample;
        }
        return (total / static_cast<float>(samples.size())).asMilliseconds();
    }

    float getLastMs() const {
        return samples.empty() ? 0.0f : samples.back().asMilliseconds();
    }
};
```

### 6.2 线程管理

#### sf::Thread
```cpp
#include <SFML/System.hpp>

// 线程函数
void backgroundTask() {
    for (int i = 0; i < 10; ++i) {
        printf("Background task: %d\n", i);
        sf::sleep(sf::milliseconds(500));
    }
}

// 使用线程
sf::Thread thread(&backgroundTask);
thread.launch();  // 启动线程

// 等待线程完成
thread.wait();

// 线程类
class WorkerThread {
private:
    sf::Thread thread;
    bool running;
    sf::Mutex mutex;
    std::queue<std::string> tasks;

public:
    WorkerThread() : thread(&WorkerThread::run, this), running(false) {}

    void start() {
        running = true;
        thread.launch();
    }

    void stop() {
        running = false;
        thread.wait();
    }

    void addTask(const std::string& task) {
        sf::Lock lock(mutex);
        tasks.push(task);
    }

private:
    void run() {
        while (running) {
            std::string task;
            {
                sf::Lock lock(mutex);
                if (!tasks.empty()) {
                    task = tasks.front();
                    tasks.pop();
                }
            }

            if (!task.empty()) {
                processTask(task);
            } else {
                sf::sleep(sf::milliseconds(10));
            }
        }
    }

    void processTask(const std::string& task) {
        printf("Processing task: %s\n", task.c_str());
        sf::sleep(sf::milliseconds(100));  // 模拟工作
    }
};
```

### 6.3 文件操作

#### 文件流
```cpp
#include <SFML/System.hpp>

// 读取文件
sf::FileInputStream fileStream;
if (fileStream.open("data.txt")) {
    sf::Int64 fileSize = fileStream.getSize();
    std::vector<char> buffer(fileSize);
    fileStream.read(buffer.data(), fileSize);

    std::string content(buffer.begin(), buffer.end());
    printf("File content: %s\n", content.c_str());
}

// 内存流
std::vector<sf::Uint8> data = {0x48, 0x65, 0x6C, 0x6C, 0x6F};  // "Hello"
sf::MemoryInputStream memStream;
memStream.open(data.data(), data.size());

char buffer[6];
memStream.read(buffer, 5);
buffer[5] = '\0';
printf("Memory content: %s\n", buffer);
```

### 6.4 向量数学

#### sf::Vector2和sf::Vector3
```cpp
// 2D向量
sf::Vector2f vec1(3.0f, 4.0f);
sf::Vector2f vec2(1.0f, 2.0f);

// 向量运算
sf::Vector2f sum = vec1 + vec2;
sf::Vector2f diff = vec1 - vec2;
sf::Vector2f scaled = vec1 * 2.0f;
sf::Vector2f divided = vec1 / 2.0f;

// 向量函数
float length(const sf::Vector2f& vec) {
    return std::sqrt(vec.x * vec.x + vec.y * vec.y);
}

sf::Vector2f normalize(const sf::Vector2f& vec) {
    float len = length(vec);
    if (len != 0.0f) {
        return sf::Vector2f(vec.x / len, vec.y / len);
    }
    return sf::Vector2f(0.0f, 0.0f);
}

float dot(const sf::Vector2f& a, const sf::Vector2f& b) {
    return a.x * b.x + a.y * b.y;
}

float distance(const sf::Vector2f& a, const sf::Vector2f& b) {
    return length(a - b);
}

// 向量工具类
class VectorMath {
public:
    static float angle(const sf::Vector2f& vec) {
        return std::atan2(vec.y, vec.x);
    }

    static sf::Vector2f fromAngle(float angle, float magnitude = 1.0f) {
        return sf::Vector2f(std::cos(angle) * magnitude, std::sin(angle) * magnitude);
    }

    static sf::Vector2f lerp(const sf::Vector2f& a, const sf::Vector2f& b, float t) {
        return a + (b - a) * t;
    }

    static sf::Vector2f reflect(const sf::Vector2f& incident, const sf::Vector2f& normal) {
        return incident - 2.0f * dot(incident, normal) * normal;
    }
};

---

## 7. 高级渲染技术

### 7.1 着色器

#### 顶点着色器
```glsl
// vertex_shader.vert
#version 130

void main() {
    gl_Position = gl_ModelViewProjectionMatrix * gl_Vertex;
    gl_TexCoord[0] = gl_TextureMatrix[0] * gl_MultiTexCoord0;
    gl_FrontColor = gl_Color;
}
```

#### 片段着色器
```glsl
// fragment_shader.frag
#version 130

uniform sampler2D texture;
uniform float time;

void main() {
    vec2 texCoord = gl_TexCoord[0].xy;

    // 波浪效果
    texCoord.x += sin(texCoord.y * 10.0 + time) * 0.01;

    vec4 color = texture2D(texture, texCoord);
    gl_FragColor = color * gl_Color;
}
```

#### 着色器使用
```cpp
// 加载着色器
sf::Shader shader;
if (!shader.loadFromFile("vertex_shader.vert", "fragment_shader.frag")) {
    printf("Failed to load shader\n");
    return -1;
}

// 设置着色器参数
shader.setUniform("time", clock.getElapsedTime().asSeconds());
shader.setUniform("texture", sf::Shader::CurrentTexture);

// 使用着色器渲染
sf::RenderStates states;
states.shader = &shader;
window.draw(sprite, states);
```

#### 着色器效果类
```cpp
class ShaderEffect {
private:
    sf::Shader shader;
    sf::Clock clock;

public:
    bool loadEffect(const std::string& fragmentShader) {
        return shader.loadFromFile(fragmentShader, sf::Shader::Fragment);
    }

    void update() {
        shader.setUniform("time", clock.getElapsedTime().asSeconds());
    }

    void apply(sf::RenderTarget& target, sf::Drawable& drawable) {
        sf::RenderStates states;
        states.shader = &shader;
        target.draw(drawable, states);
    }

    sf::Shader& getShader() { return shader; }
};

// 使用示例
ShaderEffect waveEffect;
waveEffect.loadEffect("wave_effect.frag");

// 在渲染循环中
waveEffect.update();
waveEffect.apply(window, sprite);
```

### 7.2 渲染状态

#### sf::RenderStates
```cpp
// 创建渲染状态
sf::RenderStates states;

// 设置混合模式
states.blendMode = sf::BlendAlpha;      // 标准透明混合
states.blendMode = sf::BlendAdd;        // 加法混合
states.blendMode = sf::BlendMultiply;   // 乘法混合

// 设置变换
sf::Transform transform;
transform.translate(100, 50);
transform.rotate(45);
states.transform = transform;

// 设置纹理
states.texture = &texture;

// 设置着色器
states.shader = &shader;

// 使用渲染状态
window.draw(sprite, states);
```

### 7.3 顶点数组

#### sf::VertexArray
```cpp
// 创建顶点数组
sf::VertexArray vertices(sf::Triangles, 3);

// 设置顶点
vertices[0].position = sf::Vector2f(10, 10);
vertices[0].color = sf::Color::Red;
vertices[0].texCoords = sf::Vector2f(0, 0);

vertices[1].position = sf::Vector2f(100, 10);
vertices[1].color = sf::Color::Green;
vertices[1].texCoords = sf::Vector2f(1, 0);

vertices[2].position = sf::Vector2f(50, 100);
vertices[2].color = sf::Color::Blue;
vertices[2].texCoords = sf::Vector2f(0.5, 1);

// 渲染顶点数组
window.draw(vertices);
```

#### 自定义可绘制对象
```cpp
class CustomDrawable : public sf::Drawable, public sf::Transformable {
private:
    sf::VertexArray vertices;
    sf::Texture texture;

public:
    CustomDrawable() : vertices(sf::Quads, 4) {
        // 初始化顶点
        vertices[0].position = sf::Vector2f(0, 0);
        vertices[1].position = sf::Vector2f(100, 0);
        vertices[2].position = sf::Vector2f(100, 100);
        vertices[3].position = sf::Vector2f(0, 100);

        // 设置纹理坐标
        vertices[0].texCoords = sf::Vector2f(0, 0);
        vertices[1].texCoords = sf::Vector2f(1, 0);
        vertices[2].texCoords = sf::Vector2f(1, 1);
        vertices[3].texCoords = sf::Vector2f(0, 1);
    }

    void setTexture(const sf::Texture& tex) {
        texture = tex;
    }

    void setColor(sf::Color color) {
        for (int i = 0; i < 4; ++i) {
            vertices[i].color = color;
        }
    }

private:
    virtual void draw(sf::RenderTarget& target, sf::RenderStates states) const override {
        states.transform *= getTransform();
        states.texture = &texture;
        target.draw(vertices, states);
    }
};
```

---

## 9. 游戏开发模式

### 9.1 游戏循环

#### 基础游戏循环
```cpp
class Game {
private:
    sf::RenderWindow window;
    sf::Clock clock;
    sf::Time timePerFrame;
    bool running;

public:
    Game() : window(sf::VideoMode(800, 600), "Game"),
             timePerFrame(sf::seconds(1.0f / 60.0f)),
             running(true) {
        window.setFramerateLimit(60);
    }

    void run() {
        sf::Time timeSinceLastUpdate = sf::Time::Zero;

        while (running && window.isOpen()) {
            sf::Time elapsedTime = clock.restart();
            timeSinceLastUpdate += elapsedTime;

            // 固定时间步长更新
            while (timeSinceLastUpdate > timePerFrame) {
                timeSinceLastUpdate -= timePerFrame;

                processEvents();
                update(timePerFrame);
            }

            render();
        }
    }

private:
    void processEvents() {
        sf::Event event;
        while (window.pollEvent(event)) {
            if (event.type == sf::Event::Closed) {
                running = false;
            }
            handleEvent(event);
        }
    }

    void update(sf::Time deltaTime) {
        // 更新游戏逻辑
    }

    void render() {
        window.clear();
        // 渲染游戏对象
        window.display();
    }

    void handleEvent(const sf::Event& event) {
        // 处理特定事件
    }
};
```

### 9.2 场景管理

#### 场景系统
```cpp
class Scene {
public:
    virtual ~Scene() = default;
    virtual void handleEvent(const sf::Event& event) = 0;
    virtual void update(sf::Time deltaTime) = 0;
    virtual void render(sf::RenderWindow& window) = 0;
    virtual void onEnter() {}
    virtual void onExit() {}
};

class SceneManager {
private:
    std::stack<std::unique_ptr<Scene>> scenes;

public:
    void pushScene(std::unique_ptr<Scene> scene) {
        if (!scenes.empty()) {
            scenes.top()->onExit();
        }
        scenes.push(std::move(scene));
        scenes.top()->onEnter();
    }

    void popScene() {
        if (!scenes.empty()) {
            scenes.top()->onExit();
            scenes.pop();
            if (!scenes.empty()) {
                scenes.top()->onEnter();
            }
        }
    }

    void changeScene(std::unique_ptr<Scene> scene) {
        if (!scenes.empty()) {
            scenes.top()->onExit();
            scenes.pop();
        }
        scenes.push(std::move(scene));
        scenes.top()->onEnter();
    }

    void handleEvent(const sf::Event& event) {
        if (!scenes.empty()) {
            scenes.top()->handleEvent(event);
        }
    }

    void update(sf::Time deltaTime) {
        if (!scenes.empty()) {
            scenes.top()->update(deltaTime);
        }
    }

    void render(sf::RenderWindow& window) {
        if (!scenes.empty()) {
            scenes.top()->render(window);
        }
    }

    bool isEmpty() const {
        return scenes.empty();
    }
};

// 具体场景实现
class MenuScene : public Scene {
private:
    sf::Text titleText;
    sf::Text startText;
    sf::Font font;

public:
    MenuScene() {
        font.loadFromFile("arial.ttf");

        titleText.setFont(font);
        titleText.setString("Game Title");
        titleText.setCharacterSize(48);
        titleText.setPosition(300, 200);

        startText.setFont(font);
        startText.setString("Press SPACE to start");
        startText.setCharacterSize(24);
        startText.setPosition(300, 400);
    }

    void handleEvent(const sf::Event& event) override {
        if (event.type == sf::Event::KeyPressed) {
            if (event.key.code == sf::Keyboard::Space) {
                // 切换到游戏场景
                // sceneManager.changeScene(std::make_unique<GameScene>());
            }
        }
    }

    void update(sf::Time deltaTime) override {
        // 菜单动画等
    }

    void render(sf::RenderWindow& window) override {
        window.draw(titleText);
        window.draw(startText);
    }
};

### 9.3 状态机

#### 游戏状态机
```cpp
enum class GameState {
    Menu,
    Playing,
    Paused,
    GameOver
};

class StateMachine {
private:
    GameState currentState;
    GameState previousState;

public:
    StateMachine() : currentState(GameState::Menu), previousState(GameState::Menu) {}

    void changeState(GameState newState) {
        previousState = currentState;
        currentState = newState;
        onStateChange();
    }

    GameState getCurrentState() const { return currentState; }
    GameState getPreviousState() const { return previousState; }

    bool isState(GameState state) const {
        return currentState == state;
    }

private:
    void onStateChange() {
        printf("State changed from %d to %d\n",
               static_cast<int>(previousState),
               static_cast<int>(currentState));
    }
};

// 在游戏中使用
class GameWithStates {
private:
    StateMachine stateMachine;
    sf::RenderWindow window;

public:
    void handleEvent(const sf::Event& event) {
        switch (stateMachine.getCurrentState()) {
            case GameState::Menu:
                handleMenuEvent(event);
                break;
            case GameState::Playing:
                handleGameEvent(event);
                break;
            case GameState::Paused:
                handlePauseEvent(event);
                break;
            case GameState::GameOver:
                handleGameOverEvent(event);
                break;
        }
    }

    void update(sf::Time deltaTime) {
        switch (stateMachine.getCurrentState()) {
            case GameState::Playing:
                updateGame(deltaTime);
                break;
            case GameState::Paused:
                // 暂停时不更新游戏逻辑
                break;
            // 其他状态...
        }
    }

private:
    void handleMenuEvent(const sf::Event& event) {
        if (event.type == sf::Event::KeyPressed && event.key.code == sf::Keyboard::Space) {
            stateMachine.changeState(GameState::Playing);
        }
    }

    void handleGameEvent(const sf::Event& event) {
        if (event.type == sf::Event::KeyPressed && event.key.code == sf::Keyboard::Escape) {
            stateMachine.changeState(GameState::Paused);
        }
    }

    void handlePauseEvent(const sf::Event& event) {
        if (event.type == sf::Event::KeyPressed && event.key.code == sf::Keyboard::Escape) {
            stateMachine.changeState(GameState::Playing);
        }
    }

    void handleGameOverEvent(const sf::Event& event) {
        if (event.type == sf::Event::KeyPressed && event.key.code == sf::Keyboard::R) {
            stateMachine.changeState(GameState::Playing);
            resetGame();
        }
    }

    void updateGame(sf::Time deltaTime) {
        // 游戏逻辑更新
    }

    void resetGame() {
        // 重置游戏状态
    }
};
```

---

## 11. 实际应用示例

### 11.1 2D平台游戏

#### 玩家控制器
```cpp
class Player {
private:
    sf::Sprite sprite;
    sf::Vector2f velocity;
    sf::Vector2f position;
    bool onGround;
    float speed;
    float jumpStrength;
    float gravity;

public:
    Player(const sf::Texture& texture) :
        velocity(0, 0), onGround(false), speed(200.0f),
        jumpStrength(400.0f), gravity(800.0f) {
        sprite.setTexture(texture);
        sprite.setOrigin(sprite.getLocalBounds().width / 2, sprite.getLocalBounds().height);
    }

    void handleInput() {
        velocity.x = 0;

        if (sf::Keyboard::isKeyPressed(sf::Keyboard::Left)) {
            velocity.x = -speed;
        }
        if (sf::Keyboard::isKeyPressed(sf::Keyboard::Right)) {
            velocity.x = speed;
        }
        if (sf::Keyboard::isKeyPressed(sf::Keyboard::Space) && onGround) {
            velocity.y = -jumpStrength;
            onGround = false;
        }
    }

    void update(sf::Time deltaTime) {
        float dt = deltaTime.asSeconds();

        // 应用重力
        if (!onGround) {
            velocity.y += gravity * dt;
        }

        // 更新位置
        position += velocity * dt;
        sprite.setPosition(position);

        // 简单地面碰撞检测
        if (position.y > 500) {  // 地面高度
            position.y = 500;
            velocity.y = 0;
            onGround = true;
            sprite.setPosition(position);
        }
    }

    void draw(sf::RenderWindow& window) {
        window.draw(sprite);
    }

    sf::Vector2f getPosition() const { return position; }
    void setPosition(sf::Vector2f pos) {
        position = pos;
        sprite.setPosition(position);
    }
};
```

### 11.2 粒子系统

#### 粒子类
```cpp
struct Particle {
    sf::Vector2f position;
    sf::Vector2f velocity;
    sf::Color color;
    float lifetime;
    float maxLifetime;
    float size;

    Particle(sf::Vector2f pos, sf::Vector2f vel, sf::Color col, float life, float sz) :
        position(pos), velocity(vel), color(col), lifetime(life), maxLifetime(life), size(sz) {}

    void update(sf::Time deltaTime) {
        float dt = deltaTime.asSeconds();

        position += velocity * dt;
        lifetime -= dt;

        // 淡出效果
        float alpha = lifetime / maxLifetime;
        color.a = static_cast<sf::Uint8>(255 * alpha);
    }

    bool isAlive() const {
        return lifetime > 0;
    }
};

class ParticleSystem {
private:
    std::vector<Particle> particles;
    sf::VertexArray vertices;
    sf::Texture texture;

public:
    ParticleSystem(unsigned int count) : vertices(sf::Quads, count * 4) {}

    void setTexture(const sf::Texture& tex) {
        texture = tex;
    }

    void emit(sf::Vector2f position, int count) {
        for (int i = 0; i < count; ++i) {
            // 随机速度
            float angle = (rand() % 360) * M_PI / 180.0f;
            float speed = 50 + rand() % 100;
            sf::Vector2f velocity(cos(angle) * speed, sin(angle) * speed);

            // 随机颜色
            sf::Color color(
                255,
                100 + rand() % 155,
                rand() % 100,
                255
            );

            float lifetime = 1.0f + (rand() % 200) / 100.0f;
            float size = 2.0f + (rand() % 300) / 100.0f;

            particles.emplace_back(position, velocity, color, lifetime, size);
        }
    }

    void update(sf::Time deltaTime) {
        // 更新粒子
        for (auto& particle : particles) {
            particle.update(deltaTime);
        }

        // 移除死亡粒子
        particles.erase(
            std::remove_if(particles.begin(), particles.end(),
                [](const Particle& p) { return !p.isAlive(); }),
            particles.end()
        );

        // 更新顶点数组
        vertices.resize(particles.size() * 4);
        for (size_t i = 0; i < particles.size(); ++i) {
            const Particle& p = particles[i];

            sf::Vertex* quad = &vertices[i * 4];

            float halfSize = p.size / 2.0f;
            quad[0].position = sf::Vector2f(p.position.x - halfSize, p.position.y - halfSize);
            quad[1].position = sf::Vector2f(p.position.x + halfSize, p.position.y - halfSize);
            quad[2].position = sf::Vector2f(p.position.x + halfSize, p.position.y + halfSize);
            quad[3].position = sf::Vector2f(p.position.x - halfSize, p.position.y + halfSize);

            quad[0].color = quad[1].color = quad[2].color = quad[3].color = p.color;

            // 纹理坐标
            quad[0].texCoords = sf::Vector2f(0, 0);
            quad[1].texCoords = sf::Vector2f(texture.getSize().x, 0);
            quad[2].texCoords = sf::Vector2f(texture.getSize().x, texture.getSize().y);
            quad[3].texCoords = sf::Vector2f(0, texture.getSize().y);
        }
    }

    void draw(sf::RenderWindow& window) {
        sf::RenderStates states;
        states.texture = &texture;
        states.blendMode = sf::BlendAdd;  // 加法混合
        window.draw(vertices, states);
    }

    size_t getParticleCount() const {
        return particles.size();
    }
};

// 使用示例
ParticleSystem explosionEffect(1000);
explosionEffect.setTexture(particleTexture);

// 在爆炸位置发射粒子
explosionEffect.emit(sf::Vector2f(400, 300), 50);

// 在游戏循环中
explosionEffect.update(deltaTime);
explosionEffect.draw(window);

---

## 10. 性能优化

### 10.1 渲染优化

#### 批处理渲染
```cpp
class SpriteBatch {
private:
    sf::VertexArray vertices;
    const sf::Texture* currentTexture;
    std::vector<sf::Sprite> sprites;

public:
    SpriteBatch() : vertices(sf::Quads), currentTexture(nullptr) {}

    void begin(const sf::Texture& texture) {
        currentTexture = &texture;
        vertices.clear();
        sprites.clear();
    }

    void addSprite(const sf::Sprite& sprite) {
        if (sprite.getTexture() == currentTexture) {
            sprites.push_back(sprite);
        }
    }

    void end(sf::RenderWindow& window) {
        if (sprites.empty()) return;

        // 构建顶点数组
        vertices.resize(sprites.size() * 4);

        for (size_t i = 0; i < sprites.size(); ++i) {
            const sf::Sprite& sprite = sprites[i];
            sf::Vertex* quad = &vertices[i * 4];

            sf::FloatRect bounds = sprite.getLocalBounds();
            sf::Transform transform = sprite.getTransform();

            // 计算四个顶点
            quad[0].position = transform.transformPoint(0, 0);
            quad[1].position = transform.transformPoint(bounds.width, 0);
            quad[2].position = transform.transformPoint(bounds.width, bounds.height);
            quad[3].position = transform.transformPoint(0, bounds.height);

            // 纹理坐标
            sf::IntRect texRect = sprite.getTextureRect();
            quad[0].texCoords = sf::Vector2f(texRect.left, texRect.top);
            quad[1].texCoords = sf::Vector2f(texRect.left + texRect.width, texRect.top);
            quad[2].texCoords = sf::Vector2f(texRect.left + texRect.width, texRect.top + texRect.height);
            quad[3].texCoords = sf::Vector2f(texRect.left, texRect.top + texRect.height);

            // 颜色
            sf::Color color = sprite.getColor();
            quad[0].color = quad[1].color = quad[2].color = quad[3].color = color;
        }

        // 一次性渲染所有精灵
        sf::RenderStates states;
        states.texture = currentTexture;
        window.draw(vertices, states);
    }
};

// 使用示例
SpriteBatch batch;
batch.begin(enemyTexture);

for (const auto& enemy : enemies) {
    batch.addSprite(enemy.getSprite());
}

batch.end(window);
```

#### 视锥剔除
```cpp
class FrustumCuller {
private:
    sf::FloatRect viewBounds;

public:
    void setViewBounds(const sf::View& view) {
        sf::Vector2f center = view.getCenter();
        sf::Vector2f size = view.getSize();

        viewBounds.left = center.x - size.x / 2;
        viewBounds.top = center.y - size.y / 2;
        viewBounds.width = size.x;
        viewBounds.height = size.y;
    }

    bool isVisible(const sf::Sprite& sprite) const {
        sf::FloatRect spriteBounds = sprite.getGlobalBounds();
        return viewBounds.intersects(spriteBounds);
    }

    bool isVisible(sf::Vector2f position, float radius) const {
        sf::FloatRect objectBounds(
            position.x - radius,
            position.y - radius,
            radius * 2,
            radius * 2
        );
        return viewBounds.intersects(objectBounds);
    }
};

// 使用示例
FrustumCuller culler;
culler.setViewBounds(camera.getView());

for (const auto& object : gameObjects) {
    if (culler.isVisible(object.getSprite())) {
        window.draw(object.getSprite());
    }
}
```

### 10.2 内存优化

#### 对象池
```cpp
template<typename T>
class ObjectPool {
private:
    std::vector<std::unique_ptr<T>> pool;
    std::queue<T*> available;

public:
    ObjectPool(size_t initialSize) {
        for (size_t i = 0; i < initialSize; ++i) {
            auto obj = std::make_unique<T>();
            available.push(obj.get());
            pool.push_back(std::move(obj));
        }
    }

    T* acquire() {
        if (available.empty()) {
            // 扩展池
            auto obj = std::make_unique<T>();
            T* ptr = obj.get();
            pool.push_back(std::move(obj));
            return ptr;
        }

        T* obj = available.front();
        available.pop();
        return obj;
    }

    void release(T* obj) {
        if (obj) {
            obj->reset();  // 假设T有reset方法
            available.push(obj);
        }
    }

    size_t getPoolSize() const { return pool.size(); }
    size_t getAvailableCount() const { return available.size(); }
};

// 使用示例
ObjectPool<Bullet> bulletPool(100);

// 获取子弹
Bullet* bullet = bulletPool.acquire();
bullet->initialize(position, velocity);

// 释放子弹
bulletPool.release(bullet);
```

---

## 12. 常见问题与解决方案

### 12.1 渲染问题

#### 纹理模糊
```cpp
// 问题：纹理在缩放时模糊
sf::Texture texture;
texture.loadFromFile("sprite.png");
texture.setSmooth(false);  // 禁用平滑过滤，保持像素风格

// 确保精灵位置为整数像素
sf::Sprite sprite;
sprite.setPosition(std::floor(position.x), std::floor(position.y));
```

#### 文本渲染问题
```cpp
// 问题：文本边缘锯齿
sf::Text text;
text.setFont(font);
text.setCharacterSize(24);

// 解决方案：使用合适的字体大小和抗锯齿
sf::ContextSettings settings;
settings.antialiasingLevel = 8;
sf::RenderWindow window(sf::VideoMode(800, 600), "Game", sf::Style::Default, settings);
```

### 12.2 性能问题

#### 帧率下降
```cpp
// 性能监控
class PerformanceMonitor {
private:
    sf::Clock frameClock;
    std::vector<float> frameTimes;
    size_t frameIndex;

public:
    PerformanceMonitor() : frameIndex(0) {
        frameTimes.resize(60, 0.0f);  // 记录60帧
    }

    void update() {
        float frameTime = frameClock.restart().asMilliseconds();
        frameTimes[frameIndex] = frameTime;
        frameIndex = (frameIndex + 1) % frameTimes.size();
    }

    float getAverageFrameTime() const {
        float total = 0.0f;
        for (float time : frameTimes) {
            total += time;
        }
        return total / frameTimes.size();
    }

    float getFPS() const {
        float avgFrameTime = getAverageFrameTime();
        return avgFrameTime > 0 ? 1000.0f / avgFrameTime : 0.0f;
    }
};

// 使用示例
PerformanceMonitor perfMon;

// 在游戏循环中
perfMon.update();
if (perfMon.getFPS() < 30.0f) {
    printf("Warning: Low FPS detected: %.1f\n", perfMon.getFPS());
}
```

### 12.3 平台兼容性

#### 路径分隔符
```cpp
#ifdef _WIN32
    const char PATH_SEPARATOR = '\\';
#else
    const char PATH_SEPARATOR = '/';
#endif

std::string buildPath(const std::string& dir, const std::string& filename) {
    return dir + PATH_SEPARATOR + filename;
}
```

#### 资源路径
```cpp
class ResourcePath {
public:
    static std::string getResourcePath() {
#ifdef __APPLE__
        // macOS bundle路径
        return getMainBundlePath() + "/Contents/Resources/";
#else
        // Windows和Linux
        return "./resources/";
#endif
    }

    static std::string getAssetPath(const std::string& filename) {
        return getResourcePath() + filename;
    }
};

// 使用示例
sf::Texture texture;
texture.loadFromFile(ResourcePath::getAssetPath("player.png"));
```

### 12.4 调试技巧

#### 调试渲染
```cpp
class DebugRenderer {
private:
    sf::Font debugFont;
    std::vector<std::string> debugLines;

public:
    DebugRenderer() {
        debugFont.loadFromFile("arial.ttf");
    }

    void addLine(const std::string& text) {
        debugLines.push_back(text);
    }

    void addValue(const std::string& name, float value) {
        char buffer[256];
        sprintf(buffer, "%s: %.2f", name.c_str(), value);
        debugLines.push_back(buffer);
    }

    void render(sf::RenderWindow& window) {
        for (size_t i = 0; i < debugLines.size(); ++i) {
            sf::Text text;
            text.setFont(debugFont);
            text.setString(debugLines[i]);
            text.setCharacterSize(16);
            text.setFillColor(sf::Color::White);
            text.setPosition(10, 10 + i * 20);
            window.draw(text);
        }
        debugLines.clear();
    }
};

// 使用示例
DebugRenderer debugRenderer;

// 在更新循环中
debugRenderer.addValue("FPS", perfMon.getFPS());
debugRenderer.addValue("Player X", player.getPosition().x);
debugRenderer.addValue("Player Y", player.getPosition().y);

// 在渲染循环中
debugRenderer.render(window);
```

---

## 总结

这份SFML 2.6完整使用指南涵盖了：

1. **基础概念** - 模块结构、坐标系统、时间管理
2. **窗口管理** - 创建、事件处理、状态管理
3. **图形渲染** - 基础图形、精灵、文本、纹理、变换、视图
4. **音频系统** - 音效、音乐、3D音频
5. **网络通信** - TCP、UDP、HTTP
6. **系统功能** - 时间、线程、文件、向量
7. **高级技术** - 着色器、渲染状态、顶点数组
8. **开发模式** - 游戏循环、场景管理、状态机
9. **实际应用** - 平台游戏、粒子系统
10. **性能优化** - 渲染优化、内存管理
11. **问题解决** - 常见问题和调试技巧

这是一个完整的SFML参考手册，适合从初学者到高级开发者使用！
```
```
```
```
```
```
```
```
```
