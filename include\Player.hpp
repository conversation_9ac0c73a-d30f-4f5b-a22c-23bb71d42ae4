#include <SFML/Graphics.hpp>
#include <box2d/box2d.h>
#include "GameConfig.hpp"
#include <cmath>
//////////////////////
// #include <human.h>//
//////////////////////

// 前向声明
class Racket;
class Direction
{
public:
    Direction(sf::Vector2f direction) : direction(direction) {}
    float x() const { return direction.x; }
    float y() const { return direction.y; }
    void init_direction() { direction = sf::Vector2f(1.0, 0); }
    // 规范化
    void normalize()
    {
        float length = std::sqrt(direction.x * direction.x + direction.y * direction.y);
        if (length != 0.0f)
        {
            direction /= length;
        }
    }

private:
    sf::Vector2f direction;
};

class Player
{
public:
    Player(int playerId, sf::Color color = sf::Color::Green, b2WorldId worldId = b2_nullWorldId);
    ~Player();
    sf::Vector2f getPosition() const;
    void setPosition(float x, float y);

    void draw(sf::RenderWindow &window);
    // 移动方法
    void move(Direction direction);
    // 挥拍方法
    void swing(Direction direction);

    // 检测位置
    bool isOnGround() const;
    // 获取球拍指针
    Racket *getRacket() { return racket; }

    void setPostion(float x, float y);

private:
    int playerId;
    // 用一个大小合适的圆形表示玩家
    sf::CircleShape playerShape;
    sf::RectangleShape legShape; // 腿部（底座）
    // 玩家的位置
    sf::Vector2f position;
    // 玩家速度（大小）
    float speed;

    // 物理体
    b2WorldId physicsWorldId;
    b2BodyId physicsBody;

    // 羽毛球拍
    Racket *racket;

    void createPhysicsBody();
    void updateFromPhysics();
    void setPhysicsPostion(float x, float y);
    void setGraphicPosition(float x, float y);
    // 跳跃方法
    void jump(float jumpForce);
};