#include "../include/Racket.hpp"
#include <cmath>
#include <fstream>


Racket::Racket(b2WorldId worldId, sf::Vector2f playerPosition, sf::Color color)
    : worldId(worldId), playerPosition(playerPosition), angle(0.0f), racketColor(color), swinging(0)
{
    createPhysicsBody();
    setRenderShapes();
    angularVelocity = GameConfig::RACKET_ROTATION_SPEED;
}

Racket::~Racket()
{
    if (B2_IS_NON_NULL(handleBody) && B2_IS_NON_NULL(worldId))
    {
        b2DestroyBody(handleBody);
    }
    if (B2_IS_NON_NULL(headBody) && B2_IS_NON_NULL(worldId))
    {
        b2DestroyBody(headBody);
    }
}
void Racket::setRenderShapes()
{
    // 设置渲染形状
    handleShape.setSize(sf::Vector2f(GameConfig::getHandleWidth(), GameConfig::getHandleLength()));
    handleShape.setOrigin(GameConfig::getHandleWidth() / 2.0f, GameConfig::getHandleLength()); // 从底部开始
    handleShape.setFillColor(sf::Color(139, 69, 19));                                          // 棕色拍柄

    headShape.setSize(sf::Vector2f(GameConfig::getHeadWidth(), GameConfig::getHeadHeight()));
    headShape.setOrigin(GameConfig::getHeadWidth() / 2.0f, GameConfig::getHeadHeight()); // 从底部开始
    headShape.setFillColor(racketColor);
}

void Racket::createPhysicsBody()
{
    b2BodyDef handleDef = b2DefaultBodyDef();
    // 球拍柄作为运动学物体，不参与物理模拟
    handleDef.type = b2_kinematicBody;                     
    handleDef.position = PhysicsWorld::sfToB2(playerPosition);
    handleDef.rotation = b2MakeRot(angle);
    handleBody = b2CreateBody(worldId, &handleDef);

    b2BodyDef headDef = b2DefaultBodyDef();
    headDef.type = b2_dynamicBody;
    headDef.position = PhysicsWorld::sfToB2(playerPosition);
    headDef.rotation = b2MakeRot(angle);
    headBody = b2CreateBody(worldId, &headDef);

    // 创建矩形碰撞体
    b2Polygon headBox = b2MakeBox(
        (GameConfig::getHeadWidth() / 2.0f) / PhysicsWorld::PIXELS_PER_METER,
        (GameConfig::getHeadHeight() / 2.0f) / PhysicsWorld::PIXELS_PER_METER);
    b2ShapeDef headShapeDef = b2DefaultShapeDef();
    headShapeDef.density = 2.0f;

    b2ShapeId headShapeId = b2CreatePolygonShape(headBody, &headShapeDef, &headBox);

    b2Shape_SetRestitution(headShapeId, 1.0f);

    b2Polygon handleBox = b2MakeBox(
        (GameConfig::getHandleWidth() / 2.0f) / PhysicsWorld::PIXELS_PER_METER,
        (GameConfig::getHandleLength() / 2.0f) / PhysicsWorld::PIXELS_PER_METER);
    b2ShapeDef handleShapeDef = b2DefaultShapeDef();
    handleShapeDef.isSensor = true;
    b2ShapeId handleShapeId = b2CreatePolygonShape(handleBody, &handleShapeDef, &handleBox);

    // 创建固定关节连接
    b2WeldJointDef jointDef = b2DefaultWeldJointDef();

    // 计算关节位置
    // 在球杆顶部中心
    b2Vec2 localAnchorA = {0.0f, GameConfig::getHandleLength() / 2.0f / PhysicsWorld::PIXELS_PER_METER};
    b2Vec2 localAnchorB = {0.0f, -GameConfig::getHeadHeight() / 2.0f / PhysicsWorld::PIXELS_PER_METER};
    // 设置关节连接球拍和球杆
    jointDef.base.bodyIdA = handleBody;
    jointDef.base.bodyIdB = headBody;
    jointDef.base.localFrameA.p = localAnchorA;
    jointDef.base.localFrameB.p = localAnchorB;

    b2JointId jointId = b2CreateWeldJoint(worldId, &jointDef);
}

void Racket::updateState()
{
    // 更新渲染位置和角度
    if (B2_IS_NON_NULL(handleBody))
    {
        angle = b2Rot_GetAngle(b2Body_GetRotation(handleBody));
        handleShape.setPosition(playerPosition);
        handleShape.setRotation(angle * 180.0f / M_PI); // SFML使用度数
        headShape.setPosition(playerPosition);
        headShape.setRotation(angle * 180.0f / M_PI);
    }
}

void Racket::updatePosition(sf::Vector2f newPlayerPosition)
{
    // 更新球拍位置跟随玩家
    playerPosition = newPlayerPosition;

    // 计算球拍相对于玩家的偏移位置（在玩家右侧）
    sf::Vector2f offset(GameConfig::getPlayerRadius() + 10.0f, 0.0f);
    sf::Vector2f racketPos = playerPosition + offset;

    if (B2_IS_NON_NULL(handleBody))
    {
        // 更新物理体位置，保持当前角度
        b2Rot currentRot = b2Body_GetRotation(handleBody);
        b2Body_SetTransform(handleBody, PhysicsWorld::sfToB2(racketPos), currentRot);
    }
}

void Racket::draw(sf::RenderWindow &window)
{
    updateState();
    window.draw(handleShape);
    window.draw(headShape);
}

void Racket::createJoint(b2WorldId worldId, b2BodyId playerBody, b2Vec2 playerAnchor)
{
    // 不再创建物理关节，球拍位置由代码直接控制
    // 球拍是玩家的附属，跟随玩家移动

    //***********
    std::ofstream debugLog("physics_debug.txt", std::ios::app);
    debugLog << "Racket joint creation skipped - racket is now player attachment" << std::endl;
    debugLog.close();
    //***********
}

void Racket::setAngle(float TargetAngle)
{
    // 直接设置球拍角度，不影响玩家
    if (B2_IS_NON_NULL(handleBody))
    {
        // 保持当前位置，只改变角度
        b2Vec2 currentPos = b2Body_GetPosition(handleBody);
        b2Body_SetTransform(handleBody, currentPos, b2MakeRot(TargetAngle));
        angle = TargetAngle;
    }
}

void Racket::rotate(float direction)
{
    // 连续旋转挥拍逻辑
    if (B2_IS_NULL(handleBody))
    {
        return;
    }

    // 获取当前角度并连续旋转
    float currentAngle = b2Rot_GetAngle(b2Body_GetRotation(handleBody));
    float rotationSpeed = GameConfig::RACKET_ROTATION_SPEED; // 使用配置的旋转速度

    if (direction > 0)
    {
        // 逆时针连续旋转
        angle = currentAngle + rotationSpeed * (1.0f / 60.0f); // 假设60FPS
        swinging = 1;
    }
    else
    {
        // 顺时针连续旋转
        angle = currentAngle - rotationSpeed * (1.0f / 60.0f);
        swinging = 2;
    }

    // 应用新角度
    setAngle(angle);
}

void Racket::resetAngle()
{
    // 重置到水平位置
    if (B2_IS_NON_NULL(handleBody))
    {
        setAngle(0.0f);
        angle = 0.0f;
        swinging = 0;
    }
}

void Racket::update()
{
    // 如果没有在挥拍，逐渐回到水平位置
    if (swinging == 0)
    {
        float currentAngle = b2Rot_GetAngle(b2Body_GetRotation(handleBody));
        if (std::abs(currentAngle) > 0.01f)
        {
            // 逐渐回到水平位置
            float resetSpeed = GameConfig::RACKET_ROTATION_SPEED * 0.5f;
            float newAngle = currentAngle * 0.95f; // 逐渐减小角度
            setAngle(newAngle);
        }
    }
}
