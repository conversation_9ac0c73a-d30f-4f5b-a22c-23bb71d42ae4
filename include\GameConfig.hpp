#pragma once
#include<SFML/Graphics.hpp>
// 全局游戏配置
// 参数尽量不设置成单独的值，根据窗口大小放缩，便于后续调整
class GameConfig
{
public:
    // 窗口配置
    static int WINDOW_WIDTH;
    static int WINDOW_HEIGHT;
    
    //开始界面文本位置参数
    static sf::Vector2f getTitleTextPos() { return sf::Vector2f(WINDOW_WIDTH / 3, WINDOW_HEIGHT / 3); }
    static sf::Vector2f getStartTextPos() { return sf::Vector2f(WINDOW_WIDTH / 2, WINDOW_HEIGHT / 2); }
    static sf::Vector2f getInstructionTextPos() { return sf::Vector2f(WINDOW_WIDTH / 5, WINDOW_HEIGHT / 2 + 50); }


    // 物理世界配置
    static const float PIXELS_PER_METER; // 一米的像素数
    static const float GRAVITY;          // 重力大小

    // 地面配置 - 按真实羽毛球场比例调整
    static float getGroundThickness() { return WINDOW_HEIGHT * 0.025f; } // 更薄的地面
    static float getGroundY() { return WINDOW_HEIGHT - getGroundThickness(); }

    // 墙壁配置
    static float getWallThickness() { return WINDOW_WIDTH * 0.01f; } // 更薄的墙壁
    static float getLeftWallX() { return getWallThickness() / 2.0f; }
    static float getRightWallX() { return WINDOW_WIDTH - getWallThickness() / 2.0f; }

    // 羽毛球网配置 - 按真实比例，网高1.55m
    static float getNetWidth() { return WINDOW_WIDTH * 0.003f; } // 更细的网线
    static float getNetHeight() { return WINDOW_HEIGHT * 0.2f; } // 网高约占屏幕20%
    static float getNetX() { return WINDOW_WIDTH / 2.0f; }
    static float getNetY() { return WINDOW_HEIGHT - getNetHeight() / 2.0f - getGroundThickness(); }

    // 玩家配置 - 缩小人物
    static float getPlayerRadius() { return WINDOW_WIDTH * 0.015f; } // 12像素在800宽度窗口
    static const float PLAYER_SPEED;
    static sf::Vector2f getPlayer1StartPos() { return sf::Vector2f(WINDOW_WIDTH * 0.15f, WINDOW_HEIGHT * 0.85f); }
    static sf::Vector2f getPlayer2StartPos() { return sf::Vector2f(WINDOW_WIDTH * 0.85f, WINDOW_HEIGHT * 0.85f); }

    // 球拍配置 - 修复连接问题
    static float getRacketLength() { return WINDOW_WIDTH * 0.1f; }   // 80像素在800宽度窗口
    static float getHandleLength() { return WINDOW_WIDTH * 0.075f; } // 60像素在800宽度窗口
    static float getHandleWidth() { return WINDOW_WIDTH * 0.005f; }  // 4像素在800宽度窗口，细拍柄
    static float getHeadWidth() { return getHandleWidth() + 2.0f; }  // 比拍柄粗2像素
    static float getHeadHeight() { return WINDOW_WIDTH * 0.035f; }   // 28像素在800宽度窗口，更长的拍头
    static const float RACKET_ROTATION_SPEED;
    static const sf::Color racketColor;

    // 羽毛球配置
    static float getBallRadius() { return WINDOW_WIDTH * 0.00625f; } // 5像素在800宽度窗口
    static const float BALL_DENSITY;
    static const float BALL_RESTITUTION;

    // 跳跃配置
    static const float JUMP_FORCE;

    // 初始化函数
    static void initialize(int windowWidth, int windowHeight);
};
